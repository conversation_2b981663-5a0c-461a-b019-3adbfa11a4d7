# 🚨 文件上传权限问题 - 快速修复

## 问题
上传文件时出现错误：`StorageApiError: new row violates row-level security policy`

## 🔧 快速解决方案

### 方法1：使用SQL脚本（推荐）

1. **登录Supabase Dashboard**
   - 访问 https://supabase.com/dashboard
   - 选择您的项目

2. **运行SQL脚本**
   - 点击左侧菜单的 **SQL Editor**
   - 点击 **New query**
   - 复制粘贴 `supabase-storage-setup.sql` 文件中的内容
   - 点击 **Run** 执行脚本

3. **验证结果**
   - 脚本执行后会显示配置结果
   - 确认看到 "Storage setup completed!" 消息

### 方法2：手动配置

1. **创建存储桶**
   - 点击 **Storage** → **Create a new bucket**
   - 桶名称：`games.source`
   - 设置为 **Public bucket**
   - 点击 **Create bucket**

2. **配置策略**
   - 点击刚创建的桶 → **Policies** 标签
   - 点击 **New Policy** → **For full customization**
   
   **上传策略**：
   - 名称：`Allow authenticated users to upload`
   - 操作：**INSERT**
   - 策略：`(auth.role() = 'authenticated')`
   
   **读取策略**：
   - 名称：`Allow public read access`
   - 操作：**SELECT**
   - 策略：`true`

## ✅ 验证修复

修复完成后：

1. **刷新页面**
   - 重新加载 http://localhost:3001/admin/products/add

2. **测试上传**
   - 拖拽一张图片到主图上传区域
   - 应该看到上传进度和成功消息

3. **检查错误**
   - 如果仍有问题，打开浏览器开发者工具
   - 查看 Console 标签中的错误信息

## 🔍 故障排除

### 如果仍然失败：

1. **检查用户认证**
   ```javascript
   // 在浏览器控制台运行
   console.log('User authenticated:', !!window.localStorage.getItem('supabase.auth.token'));
   ```

2. **检查存储桶**
   - 在Supabase Dashboard → Storage 中确认 `games.source` 桶存在

3. **检查策略**
   - 在存储桶的 Policies 标签中确认策略已创建

4. **临时解决方案**
   - 如果急需测试，可以临时禁用RLS：
   ```sql
   ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
   ```
   ⚠️ **注意**：这会降低安全性，仅用于开发测试

## 📞 需要帮助？

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. Supabase项目的存储桶配置截图
3. 用户认证状态

现在应该可以正常上传文件了！🎉
