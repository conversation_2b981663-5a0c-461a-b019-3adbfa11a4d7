# 商品媒体和富文本功能测试指南

## 🧪 测试环境

- **开发服务器**: http://localhost:3001
- **测试页面**: http://localhost:3001/admin/products/add
- **用户权限**: 需要管理员权限

## ✅ 功能测试清单

### 1. 商品添加表单基础功能

#### 基本信息填写
- [ ] 商品名称输入
- [ ] 商品描述输入
- [ ] 平台选择（Steam, Xbox, PlayStation等）
- [ ] 分类选择
- [ ] 价格输入
- [ ] 货币选择

#### 媒体上传功能
- [ ] **主图上传**
  - [ ] 点击上传区域选择文件
  - [ ] 拖拽文件到上传区域
  - [ ] 支持的格式：JPG, PNG, WebP
  - [ ] 文件大小限制：10MB
  - [ ] 上传进度显示
  - [ ] 上传成功后显示预览
  - [ ] 删除已上传的主图

- [ ] **视频上传**
  - [ ] 点击上传区域选择视频文件
  - [ ] 拖拽视频文件到上传区域
  - [ ] 支持的格式：MP4, WebM, MOV
  - [ ] 文件大小限制：10MB
  - [ ] 上传进度显示
  - [ ] 上传成功后显示视频预览
  - [ ] 删除已上传的视频

- [ ] **图片库上传**
  - [ ] 多选图片文件上传
  - [ ] 拖拽多个图片文件
  - [ ] 最多8张图片限制
  - [ ] 图片网格预览
  - [ ] 单独删除某张图片

#### 富文本编辑器
- [ ] **基本格式化**
  - [ ] 粗体、斜体、下划线
  - [ ] 文本对齐（左、中、右）
  - [ ] 撤销和重做

- [ ] **列表功能**
  - [ ] 有序列表
  - [ ] 无序列表

- [ ] **特殊格式**
  - [ ] 引用块
  - [ ] 代码块

- [ ] **插入功能**
  - [ ] 插入链接
  - [ ] 插入图片（通过URL）

### 2. 表单提交功能

#### 数据验证
- [ ] 必填字段验证（商品名称、主图等）
- [ ] 价格格式验证
- [ ] 文件类型验证
- [ ] 文件大小验证

#### API调用
- [ ] 表单提交成功
- [ ] 错误处理显示
- [ ] 加载状态显示
- [ ] 成功后跳转到商品列表

### 3. 商品详情页面展示

#### 媒体画廊
- [ ] **视频优先显示**
  - [ ] 有视频时优先显示视频
  - [ ] 视频播放控制
  - [ ] 视频播放按钮

- [ ] **图片切换**
  - [ ] 左右箭头切换
  - [ ] 缩略图点击切换
  - [ ] 图片计数显示

- [ ] **响应式设计**
  - [ ] 桌面端正常显示
  - [ ] 移动端适配

#### 富文本内容显示
- [ ] HTML内容正确渲染
- [ ] 格式化样式正确
- [ ] 链接可点击
- [ ] 图片正常显示

## 🔧 测试步骤

### 步骤1：准备测试文件
准备以下测试文件：
- 1张主图（JPG/PNG，< 10MB）
- 1个视频文件（MP4，< 10MB）
- 3-5张额外图片（JPG/PNG，< 10MB）

### 步骤2：访问添加商品页面
1. 打开 http://localhost:3001/admin/products/add
2. 确认页面正常加载
3. 确认所有表单字段都显示

### 步骤3：填写基本信息
1. 输入商品名称："测试商品 - Steam Gift Card"
2. 输入商品描述："这是一个测试商品"
3. 选择平台："Gift Card"
4. 选择分类："gift_card"
5. 输入价格："99.99"

### 步骤4：上传媒体文件
1. **上传主图**：
   - 点击主图上传区域
   - 选择准备好的主图文件
   - 等待上传完成
   - 确认预览显示正确

2. **上传视频**（可选）：
   - 点击视频上传区域
   - 选择准备好的视频文件
   - 等待上传完成
   - 确认视频预览显示

3. **上传图片库**：
   - 点击图片库上传区域
   - 选择多张图片文件
   - 等待上传完成
   - 确认图片网格显示

### 步骤5：编写富文本内容
在富文本编辑器中输入以下测试内容：
```
# 商品详情

这是一个**测试商品**，具有以下特点：

## 主要功能
- 功能1：*高质量*的游戏体验
- 功能2：__快速激活__
- 功能3：全球通用

> 这是一个引用块，用于强调重要信息。

### 系统要求
```
Windows 10 或更高版本
至少 4GB RAM
```

更多信息请访问：[官方网站](https://example.com)
```

### 步骤6：提交表单
1. 点击"创建商品"按钮
2. 确认加载状态显示
3. 等待提交完成
4. 确认成功消息显示
5. 确认跳转到商品列表

### 步骤7：验证商品详情页
1. 在商品列表中找到刚创建的商品
2. 点击查看详情
3. 验证媒体画廊功能
4. 验证富文本内容显示

## 🐛 常见问题排查

### 上传失败
- 检查文件大小是否超过10MB
- 检查文件格式是否支持
- 检查网络连接
- 查看浏览器控制台错误

### 富文本编辑器问题
- 检查浏览器兼容性
- 尝试刷新页面
- 检查JavaScript错误

### 表单提交失败
- 检查必填字段是否填写
- 检查网络连接
- 查看浏览器网络面板
- 检查服务器日志

## 📊 测试结果记录

| 功能 | 状态 | 备注 |
|------|------|------|
| 主图上传 | ⭕ | |
| 视频上传 | ⭕ | |
| 图片库上传 | ⭕ | |
| 富文本编辑 | ⭕ | |
| 表单提交 | ⭕ | |
| 媒体画廊 | ⭕ | |
| 富文本显示 | ⭕ | |

**测试完成时间**: ___________
**测试人员**: ___________
**总体评价**: ___________
