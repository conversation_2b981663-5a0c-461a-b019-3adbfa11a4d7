import { config } from 'dotenv';
import { defineConfig } from 'drizzle-kit';

// Ensure environment variables from `.env.local` are available when running
// Drizzle Kit in scripts outside of Next.js.
config({ path: '.env.local' });

export default defineConfig({
  schema: './lib/db/schema.ts',
  out: './drizzle/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
});