# 🎮 Digital Game Store

A modern digital game code marketplace built with Next.js, Supabase, and Stripe. This platform allows users to purchase and instantly receive digital activation codes for various gaming platforms.

## ✨ Features

### Core Functionality
- 🛍️ **Product Catalog**: Browse digital game codes by platform (Nintendo Switch, PlayStation, Xbox, Steam, Gift Cards)
- 🔍 **Advanced Search & Filtering**: Filter by platform, region, and search by game name
- 💳 **Secure Payments**: Stripe integration with fraud detection and risk assessment
- ⚡ **Instant Delivery**: Automatic activation code delivery via email
- 📧 **Email Notifications**: Order confirmations, delivery notifications, and status updates
- 👤 **User Dashboard**: Order history and activation code management

### Security & Compliance
- 🛡️ **Risk Assessment**: Automated fraud detection using Stripe Radar
- 🔐 **Secure Code Storage**: Activation codes are masked and encrypted
- 📊 **Audit Logging**: Complete audit trail for all system actions
- 🔒 **Row Level Security**: Database-level security with Supabase RLS

### Technical Features
- 🚀 **Modern Stack**: Next.js 15, TypeScript, Tailwind CSS, Drizzle ORM
- 📱 **Responsive Design**: Mobile-first design with shadcn/ui components
- 🔄 **Real-time Updates**: Live order status updates
- 📈 **Scalable Architecture**: Built for high-volume transactions

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (via Supabase)
- **ORM**: Drizzle ORM
- **Payments**: Stripe
- **Email**: Resend
- **Authentication**: Supabase Auth
- **Deployment**: Vercel (recommended)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and pnpm
- Supabase account and project
- Stripe account
- Resend account (for emails)

### 1. Clone and Install
```bash
git clone <repository-url>
cd digital-game-store
pnpm install
```

### 2. Environment Setup
Copy `.env.example` to `.env.local` and fill in your credentials:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
DATABASE_URL=your-database-url

# Stripe
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Email (Resend)
RESEND_API_KEY=re_your-resend-api-key
FROM_EMAIL=<EMAIL>

# App Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
ADMIN_EMAIL=<EMAIL>
```

### 3. Verify Setup
```bash
# Check if all environment variables are set
pnpm check-env
```

### 4. Database Setup
```bash
# Generate and run migrations
pnpm drizzle:generate
pnpm drizzle:push

# Seed with sample data
pnpm seed
```

### 5. Start Development Server
```bash
pnpm dev
```

Visit [http://localhost:3000](http://localhost:3000) to see your store!

## 🔧 Configuration

### Stripe Webhooks
Set up a webhook endpoint in your Stripe dashboard:
- URL: `https://yourdomain.com/api/webhooks/stripe`
- Events: `payment_intent.succeeded`, `payment_intent.payment_failed`

### Email Templates
The system uses Resend for email delivery. Templates are built-in but can be customized in `lib/services/email.ts`.

## 🚦 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm check-env` - Verify environment variables
- `pnpm drizzle:generate` - Generate database migrations
- `pnpm drizzle:push` - Apply database migrations
- `pnpm seed` - Seed database with sample data

## 🐛 Troubleshooting

### Database Connection Issues
If you see "password authentication failed" errors:

1. **Check environment variables**:
   ```bash
   pnpm check-env
   ```

2. **Verify DATABASE_URL format**:
   ```
   postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres
   ```

3. **Get correct credentials from Supabase**:
   - Go to your Supabase project dashboard
   - Navigate to Settings > Database
   - Copy the connection string under "Connection string"

### Environment Variable Issues
If you see "Missing env var" errors:

1. **Copy the example file**:
   ```bash
   cp .env.example .env.local
   ```

2. **Fill in all required values** (see Environment Variables section)

3. **Restart the development server**:
   ```bash
   pnpm dev
   ```

### Next.js 15 Compatibility
This project uses Next.js 15 with the new async `searchParams`. If you encounter issues:

1. **Make sure you're using Node.js 18+**
2. **Clear Next.js cache**:
   ```bash
   rm -rf .next
   pnpm dev
   ```

## 📝 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | ✅ |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | ✅ |
| `DATABASE_URL` | PostgreSQL connection string | ✅ |
| `STRIPE_SECRET_KEY` | Stripe secret key | ✅ |
| `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` | Stripe publishable key | ✅ |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook secret | ✅ |
| `RESEND_API_KEY` | Resend API key | ✅ |
| `FROM_EMAIL` | Sender email address | ✅ |
| `NEXT_PUBLIC_APP_URL` | Application URL | ✅ |
| `ADMIN_EMAIL` | Admin email address | ❌ |

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment
1. Build the application: `pnpm build`
2. Start production server: `pnpm start`

## 📚 Key Features Implementation

### Automatic Code Delivery
- Uses `FOR UPDATE SKIP LOCKED` for race-condition-free code allocation
- Webhook-driven delivery system
- Email notifications with masked activation codes

### Risk Assessment
- Stripe Radar integration
- Custom risk scoring based on payment patterns
- Automatic review queue for high-risk orders

### Security
- Row Level Security (RLS) policies
- Audit logging for all critical actions
- Encrypted activation code storage
- CSRF protection and input validation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Run `pnpm check-env` to verify your setup
- Check the troubleshooting section above
- Create an issue in the GitHub repository
- Review the PRD.md for detailed requirements

---

Built with ❤️ using Next.js, Supabase, and Stripe
