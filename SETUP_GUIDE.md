# 🚀 Digital Game Store - 详细设置指南

这个指南将帮助您获取所有必需的 API 密钥和配置信息。

## 📋 当前状态

运行 `pnpm check-env` 显示以下环境变量需要真实值：

- ⚠️ DATABASE_URL (需要数据库密码)
- ⚠️ STRIPE_SECRET_KEY (需要 Stripe 密钥)
- ⚠️ NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY (需要 Stripe 公钥)
- ⚠️ STRIPE_WEBHOOK_SECRET (需要 Stripe webhook 密钥)
- ⚠️ RESEND_API_KEY (需要 Resend API 密钥)
- ⚠️ FROM_EMAIL (需要真实邮箱地址)

## 🔧 逐步设置指南

### 1. 设置 Supabase 数据库

您的 Supabase 项目已经创建：`cvoecjjubueppybpnswz`

**获取数据库密码：**

1. 访问 [Supabase Dashboard](https://app.supabase.com/project/cvoecjjubueppybpnswz)
2. 进入 **Settings** > **Database**
3. 在 **Connection string** 部分，复制 **URI** 格式的连接字符串
4. 或者，如果您记得创建项目时设置的密码，直接替换 `[YOUR-PASSWORD]`

**更新 .env.local：**
```bash
DATABASE_URL=postgresql://postgres:您的实际密码@db.cvoecjjubueppybpnswz.supabase.co:5432/postgres
```

### 2. 设置 Stripe 支付

**创建 Stripe 账户：**

1. 访问 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 注册或登录您的账户
3. 进入 **Developers** > **API keys**

**获取 API 密钥：**
- **Secret key** (以 `sk_test_` 开头)
- **Publishable key** (以 `pk_test_` 开头)

**设置 Webhook：**

1. 在 Stripe Dashboard，进入 **Developers** > **Webhooks**
2. 点击 **Add endpoint**
3. 设置 URL: `http://localhost:3000/api/webhooks/stripe` (开发环境)
4. 选择事件: `payment_intent.succeeded` 和 `payment_intent.payment_failed`
5. 创建后，复制 **Signing secret** (以 `whsec_` 开头)

**更新 .env.local：**
```bash
STRIPE_SECRET_KEY=sk_test_您的实际密钥
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_您的实际公钥
STRIPE_WEBHOOK_SECRET=whsec_您的实际webhook密钥
```

### 3. 设置 Resend 邮件服务

**创建 Resend 账户：**

1. 访问 [Resend](https://resend.com/)
2. 注册账户
3. 进入 **API Keys** 页面
4. 创建新的 API 密钥

**设置发件域名（可选）：**

1. 在 Resend Dashboard，进入 **Domains**
2. 添加您的域名并验证 DNS 记录
3. 或者使用 Resend 提供的测试域名

**更新 .env.local：**
```bash
RESEND_API_KEY=re_您的实际API密钥
FROM_EMAIL=noreply@您的域名.com
# 或者使用测试邮箱
FROM_EMAIL=<EMAIL>
```

## 🧪 测试配置

完成设置后，运行以下命令验证：

```bash
# 1. 检查环境变量
pnpm check-env

# 2. 测试数据库连接
pnpm drizzle:push

# 3. 添加示例数据
pnpm seed

# 4. 启动开发服务器
pnpm dev
```

## 🔍 故障排除

### 数据库连接失败

如果看到 "password authentication failed" 错误：

1. **重置数据库密码：**
   - 在 Supabase Dashboard > Settings > Database
   - 点击 "Reset database password"
   - 使用新密码更新 DATABASE_URL

2. **检查连接字符串格式：**
   ```
   postgresql://postgres:密码@db.项目ID.supabase.co:5432/postgres
   ```

### Stripe 测试

1. **使用测试卡号：**
   - 卡号: `4242 4242 4242 4242`
   - 过期日期: 任何未来日期
   - CVC: 任何3位数字

2. **检查 webhook 端点：**
   - 确保 URL 正确
   - 检查选择的事件类型

### 邮件发送测试

1. **使用 Resend 测试域名：**
   ```
   FROM_EMAIL=<EMAIL>
   ```

2. **检查 API 密钥权限：**
   - 确保 API 密钥有发送邮件的权限

## 📞 获取帮助

如果遇到问题：

1. **检查日志：** 查看浏览器控制台和终端输出
2. **验证配置：** 运行 `pnpm check-env`
3. **查看文档：**
   - [Supabase 文档](https://supabase.com/docs)
   - [Stripe 文档](https://stripe.com/docs)
   - [Resend 文档](https://resend.com/docs)

## ✅ 完成检查清单

- [ ] Supabase 数据库密码已设置
- [ ] Stripe API 密钥已配置
- [ ] Stripe webhook 已创建
- [ ] Resend API 密钥已获取
- [ ] 发件邮箱已设置
- [ ] `pnpm check-env` 显示全部 ✅
- [ ] 数据库迁移成功
- [ ] 示例数据已添加
- [ ] 开发服务器正常启动

完成所有步骤后，您的数字游戏商店就可以正常运行了！🎉
