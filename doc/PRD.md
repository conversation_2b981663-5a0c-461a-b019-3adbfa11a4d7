以下内容为 数字商品购买平台（买家端） 的 增强版需求与技术方案 v1.1。已将上一轮评审建议（⚠️ 合规、反欺诈、监控、审计、对账等）全面融入，方便直接复制到 Notion / GitHub / Docs 等工具。

⸻

🎮 数字商品购买平台（买家端）需求 & 技术方案 v1.1

1 🧭 产品定位

项目	说明
目标用户	全球范围内购买 Switch / PS / Xbox / Steam 等数字激活码的个人玩家子人群细分：• 🇸🇬 东南亚（信用卡 + 本地 eWallet）• 🇺🇸 北美（信用卡 + PayPal）
核心价值	一站式、官方保障、秒级发码、风控安全
差异化	⚡ 自动发货 + 7×24 AI/人工客服   🛡️ 高级反欺诈   📊 订单 & 财务对账自动化


⸻

2 📦 功能模块（增强版）

模块	MVP 功能	加强项
用户系统	注册 / 登录（邮箱、手机号、社交）订单中心 / 售后中心	• 多因子登录（邮箱 + OTP）• 安全日志（登录 IP、设备指纹）
商品系统	分类浏览、搜索、详情	• 区服/语言过滤• 库存阈值告警
下单与支付	立即购买 → Stripe / PayPal / 本地 PSP	• Stripe Radar + 自建风控评分• 高风险订单人工复核• WebHook 幂等处理
自动发货	支付成功 → 分配激活码 → 邮件发送	• FOR UPDATE SKIP LOCKED 行锁发码• 激活码遮掩显示（点击复制）• 失败重试队列 + 告警
售后系统	无效码申请 → 人工审核	• AI OCR 识别截图文字• 售后时效 SLA、自动提醒
合规 & 审计	—	• 授权资质存档（任天堂 / Sony 等）• GDPR / CCPA 数据擦除• audit_logs 全量追踪• 管理员操作双人复核
监控 & 对账	—	• Sentry + Logflare Error Tracking• Prometheus 指标 & Grafana Dashboard• 日 / 月财务对账报告（销售额、费率、退款）
通知系统	邮件发送	• DKIM / SPF 配置• 备用渠道：站内信 + 短信• 异常告警（发码失败 / 高额退款）
数据分析	—	• PostHog 埋点：浏览→下单→支付• 异常激活率监测


⸻

3 🧱 数据库模型（Supabase PostgreSQL）

erDiagram
    users {
      UUID id PK
      TEXT email
      TIMESTAMP created_at
    }
    products {
      UUID id PK
      TEXT name
      TEXT platform
      TEXT region
      NUMERIC price
      TEXT description
      BOOLEAN is_active
      TIMESTAMP created_at
    }
    activation_codes {
      UUID id PK
      UUID product_id FK "products.id"
      TEXT code
      TEXT status  -- unused|locked|used|invalid
      UUID locked_by_order_id
      TIMESTAMP expires_at
      TIMESTAMP delivered_at
    }
    orders {
      UUID id PK
      UUID user_id FK "users.id"
      UUID product_id FK "products.id"
      NUMERIC price
      TEXT status  -- pending|paid|delivered|refunded|under_review
      TIMESTAMP paid_at
      TIMESTAMP delivered_at
      TIMESTAMP refunded_at
      TEXT payment_intent_id UNIQUE
      TEXT risk_level   -- low|medium|high
      JSONB metadata
      TIMESTAMP created_at
    }
    order_deliveries {
      UUID id PK
      UUID order_id FK "orders.id"
      UUID activation_code_id FK "activation_codes.id"
      TIMESTAMP delivered_at
    }
    after_sales {
      UUID id PK
      UUID order_id FK "orders.id"
      TEXT issue_type
      TEXT description
      TEXT status  -- pending|resolved|rejected
      TEXT resolution
      TEXT evidence_url
      TIMESTAMP created_at
    }
    audit_logs {
      UUID id PK
      UUID actor_id
      TEXT actor_type   -- user|admin|system
      TEXT action
      JSONB details
      TIMESTAMP created_at
    }

🔒 RLS 样例（发码 + 订单）

-- activation_codes：仅系统函数可更新
ALTER TABLE activation_codes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "system can select unused codes"
ON activation_codes
FOR SELECT
TO authenticated
USING (
  status = 'unused'
  AND current_setting('request.jwt.claims', true)::jsonb->>'role' = 'service_role'
);

CREATE POLICY "system can update status"
ON activation_codes
FOR UPDATE
TO authenticated
USING (
  current_setting('request.jwt.claims', true)::jsonb->>'role' = 'service_role'
);

-- orders：仅本人可读写
CREATE POLICY "user can read own orders"
ON orders FOR SELECT
USING ( auth.uid() = user_id );

CREATE POLICY "user can insert order"
ON orders FOR INSERT
WITH CHECK ( auth.uid() = user_id );


⸻

4 🚀 核心流程

4.1 下单 & 发码（含风控）

sequenceDiagram
    participant U as User
    participant FE as Next.js
    participant BE as Supabase Func
    participant ST as Stripe
    participant DB as DB
    U->>FE: 点击立即购买
    FE->>BE: 创建 pending 订单
    BE->>ST: 创建 PaymentIntent
    U->>ST: 完成支付
    ST-->>BE: Webhook (succeeded)
    BE->>DB: 风控评分 & 更新 order.status=paid (事务)
    alt Risk=high
        BE->>DB: order.status=under_review
    else Risk=low/med
        BE->>DB: 发码事务<br/>1. SELECT code FOR UPDATE SKIP LOCKED<br/>2. UPDATE status=used<br/>3. INSERT order_deliveries
        BE->>U: 邮件 + 站内信
    end

4.2 售后
	1.	用户提交问题（无效 / 已用）+ 截图
	2.	AI OCR 解析验证码 → 预判真伪
	3.	管理员后台审核 → 补发 / 退款 → 更新 after_sales.resolution
	4.	记录 audit_logs，发送结果通知

⸻

5 🛡️ 反欺诈 & 安全

场景	措施
盗刷	Stripe Radar + 自建规则（国家/IP/卡段）
滥用退款	订单状态 under_review + 黑名单库
激活码泄露	前端遮掩，复制按钮；服务器加密存储
管理员越权	后台操作需二次验证 + audit_logs


⸻

6 📊 监控 & 运维

维度	工具	关键指标
应用错误	Sentry	Error rate, latency
日志	Logflare → BigQuery	High-value events
指标	Prometheus + Grafana	QPS、失败率、库存告警
告警	PagerDuty / Slack	发码失败、退款激增
对账	日/周自动 Report	GMV、手续费、净收款、退款额


⸻

7 🚀 迭代里程碑

版本	里程碑 & 验收指标
MVP (T+4 周)	• 浏览 / 下单 / Stripe 支付 / 自动发码• 激活码成功率 ≥ 99%• RPO/RTO ≤ 30 min
v1.1 (T+8 周)	• 售后工单后台• 风控评分 / 邮件 DKIM• 监控 & 对账 Dashboard
v1.2 (T+12 周)	• 多币种 & 区域定价• 邀请码返利• 营销邮件自动化


⸻

8 📚 参考链接
	•	Next.js Docs
	•	Supabase Docs
	•	Stripe Radar
	•	OWASP Cheat Sheets

⸻

若需进一步拆解接口契约 (OpenAPI)、风控规则 DSL、或后台 UI 原型，请告诉我！