#!/usr/bin/env tsx

/**
 * Environment Variables Checker
 * 
 * This script checks if all required environment variables are set
 * and provides helpful guidance for setting them up.
 */

import { config } from 'dotenv';

// Load environment variables from .env.local
config({ path: '.env.local' });

interface EnvVar {
  name: string;
  required: boolean;
  description: string;
  example?: string;
}

const requiredEnvVars: EnvVar[] = [
  {
    name: 'NEXT_PUBLIC_SUPABASE_URL',
    required: true,
    description: 'Your Supabase project URL',
    example: 'https://your-project.supabase.co'
  },
  {
    name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    required: true,
    description: 'Your Supabase anonymous key',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  },
  {
    name: 'DATABASE_URL',
    required: true,
    description: 'PostgreSQL connection string for Drizzle ORM',
    example: 'postgresql://postgres:<EMAIL>:5432/postgres'
  },
  {
    name: 'STRIPE_SECRET_KEY',
    required: true,
    description: 'Stripe secret key (starts with sk_test_ or sk_live_)',
    example: 'sk_test_...'
  },
  {
    name: 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
    required: true,
    description: 'Stripe publishable key (starts with pk_test_ or pk_live_)',
    example: 'pk_test_...'
  },
  {
    name: 'STRIPE_WEBHOOK_SECRET',
    required: true,
    description: 'Stripe webhook secret (starts with whsec_)',
    example: 'whsec_...'
  },
  {
    name: 'RESEND_API_KEY',
    required: true,
    description: 'Resend API key for sending emails',
    example: 're_...'
  },
  {
    name: 'FROM_EMAIL',
    required: true,
    description: 'Email address to send emails from',
    example: '<EMAIL>'
  },
  {
    name: 'NEXT_PUBLIC_APP_URL',
    required: true,
    description: 'Your application URL',
    example: 'http://localhost:3000'
  },
  {
    name: 'ADMIN_EMAIL',
    required: false,
    description: 'Admin email address for notifications',
    example: '<EMAIL>'
  }
];

function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...\n');

  let hasErrors = false;
  let hasWarnings = false;

  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar.name];
    
    if (!value) {
      if (envVar.required) {
        console.log(`❌ ${envVar.name} (REQUIRED)`);
        console.log(`   ${envVar.description}`);
        if (envVar.example) {
          console.log(`   Example: ${envVar.example}`);
        }
        console.log('');
        hasErrors = true;
      } else {
        console.log(`⚠️  ${envVar.name} (OPTIONAL)`);
        console.log(`   ${envVar.description}`);
        if (envVar.example) {
          console.log(`   Example: ${envVar.example}`);
        }
        console.log('');
        hasWarnings = true;
      }
    } else {
      // Check for placeholder values
      const hasPlaceholder = value.includes('[YOUR-PASSWORD]') ||
                            value.includes('your-') ||
                            value.includes('sk_test_your-') ||
                            value.includes('pk_test_your-') ||
                            value.includes('whsec_your-') ||
                            value.includes('re_your-') ||
                            value.includes('@yourdomain.com');

      if (hasPlaceholder && envVar.required) {
        console.log(`⚠️  ${envVar.name} (PLACEHOLDER DETECTED)`);
        console.log(`   Current value contains placeholder text`);
        console.log(`   ${envVar.description}`);
        if (envVar.example) {
          console.log(`   Example: ${envVar.example}`);
        }
        console.log('');
        hasWarnings = true;
      } else {
        // Mask sensitive values
        const maskedValue = value.length > 10
          ? value.substring(0, 10) + '...'
          : '***';
        console.log(`✅ ${envVar.name}: ${maskedValue}`);
      }
    }
  }

  console.log('\n' + '='.repeat(60));

  if (hasErrors) {
    console.log('\n❌ SETUP INCOMPLETE');
    console.log('\nMissing required environment variables. Please:');
    console.log('1. Copy .env.example to .env.local');
    console.log('2. Fill in the missing values');
    console.log('3. Run this script again to verify\n');
    
    console.log('📚 Setup guides:');
    console.log('• Supabase: https://supabase.com/docs/guides/getting-started');
    console.log('• Stripe: https://stripe.com/docs/keys');
    console.log('• Resend: https://resend.com/docs/introduction');
    
    process.exit(1);
  } else if (hasWarnings) {
    console.log('\n⚠️  SETUP MOSTLY COMPLETE');
    console.log('\nOptional environment variables are missing, but the app should work.');
  } else {
    console.log('\n✅ SETUP COMPLETE');
    console.log('\nAll required environment variables are set!');
  }

  console.log('\n🚀 Next steps:');
  console.log('1. Run database migrations: pnpm drizzle:push');
  console.log('2. Seed sample data: pnpm seed');
  console.log('3. Start development server: pnpm dev');
}

// Run the check
checkEnvironmentVariables();
