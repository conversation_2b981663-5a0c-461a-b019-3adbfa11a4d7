import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Load environment variables from .env.local
config({ path: '.env.local' });

// Verify DATABASE_URL is loaded
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL not found in environment variables');
  console.error('Make sure .env.local file exists and contains DATABASE_URL');
  process.exit(1);
}

// Create database connection
const queryClient = postgres(process.env.DATABASE_URL, {
  max: 1,
});
const db = drizzle(queryClient);

// Import schema after environment is loaded
import { products, activationCodes, users } from '@/lib/db/schema';

async function seed() {
  console.log('🌱 Seeding database...');

  try {
    // Create sample users
    const sampleUsers = await db.insert(users).values([
      {
        email: '<EMAIL>',
        role: 'user',
      },
      {
        email: '<EMAIL>',
        role: 'super_admin',
      },
    ]).returning();

    console.log('✅ Created sample users');

    // Create sample products
    const sampleProducts = await db.insert(products).values([
      {
        name: 'The Legend of Zelda: Breath of the Wild',
        platform: 'switch',
        region: 'US',
        currency: 'USD',
        amountCents: 5999, // $59.99
        description: 'Embark on an adventure that spans the vast world of Hyrule in this open-air adventure.',
        slug: 'zelda-breath-of-the-wild-us',
        isActive: true,
      },
      {
        name: 'Super Mario Odyssey',
        platform: 'switch',
        region: 'US',
        currency: 'USD',
        amountCents: 4999, // $49.99
        description: 'Join Mario on a massive, globe-trotting 3D adventure and use his incredible new abilities.',
        slug: 'super-mario-odyssey-us',
        isActive: true,
      },
      {
        name: 'God of War',
        platform: 'ps',
        region: 'US',
        currency: 'USD',
        amountCents: 3999, // $39.99
        description: 'His vengeance against the Gods of Olympus years behind him, Kratos now lives as a man in the realm of Norse Gods.',
        slug: 'god-of-war-ps-us',
        isActive: true,
      },
      {
        name: 'Halo Infinite',
        platform: 'xbox',
        region: 'US',
        currency: 'USD',
        amountCents: 5999, // $59.99
        description: 'When all hope is lost and humanity\'s fate hangs in the balance, Master Chief is ready to confront the most ruthless foe he\'s ever faced.',
        slug: 'halo-infinite-xbox-us',
        isActive: true,
      },
      {
        name: 'Cyberpunk 2077',
        platform: 'steam',
        region: 'Global',
        currency: 'USD',
        amountCents: 2999, // $29.99
        description: 'Cyberpunk 2077 is an open-world, action-adventure RPG set in the dark future of Night City.',
        slug: 'cyberpunk-2077-steam-global',
        isActive: true,
      },
      {
        name: 'Steam Wallet Code $20',
        platform: 'gift_card',
        region: 'US',
        currency: 'USD',
        amountCents: 2000, // $20.00
        description: 'Add funds to your Steam Wallet to purchase games, software, and other items.',
        slug: 'steam-wallet-20-us',
        isActive: true,
      },
      {
        name: 'PlayStation Store Gift Card $25',
        platform: 'gift_card',
        region: 'US',
        currency: 'USD',
        amountCents: 2500, // $25.00
        description: 'Purchase games, add-ons, and more from the PlayStation Store.',
        slug: 'psn-gift-card-25-us',
        isActive: true,
      },
    ]).returning();

    console.log('✅ Created sample products');

    // Create sample activation codes for each product
    const activationCodePromises = sampleProducts.map(async (product) => {
      const codes = [];
      const codeCount = Math.floor(Math.random() * 10) + 5; // 5-15 codes per product
      
      for (let i = 0; i < codeCount; i++) {
        // Generate fake activation codes
        const code = generateFakeActivationCode(product.platform);
        codes.push({
          productId: product.id,
          code,
          status: 'unused' as const,
        });
      }

      return db.insert(activationCodes).values(codes);
    });

    await Promise.all(activationCodePromises);

    console.log('✅ Created sample activation codes');
    console.log('🎉 Database seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

function generateFakeActivationCode(platform: string): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  
  switch (platform) {
    case 'switch':
      // Nintendo Switch codes: XXXX-XXXX-XXXX-XXXX
      return Array.from({ length: 4 }, () => 
        Array.from({ length: 4 }, () => chars[Math.floor(Math.random() * chars.length)]).join('')
      ).join('-');
    
    case 'ps':
      // PlayStation codes: XXXX-XXXX-XXXX
      return Array.from({ length: 3 }, () => 
        Array.from({ length: 4 }, () => chars[Math.floor(Math.random() * chars.length)]).join('')
      ).join('-');
    
    case 'xbox':
      // Xbox codes: XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
      return Array.from({ length: 5 }, () => 
        Array.from({ length: 5 }, () => chars[Math.floor(Math.random() * chars.length)]).join('')
      ).join('-');
    
    case 'steam':
      // Steam codes: XXXXX-XXXXX-XXXXX
      return Array.from({ length: 3 }, () => 
        Array.from({ length: 5 }, () => chars[Math.floor(Math.random() * chars.length)]).join('')
      ).join('-');
    
    case 'gift_card':
      // Gift card codes: XXXXXXXXXXXXXXXXXXXX (20 chars)
      return Array.from({ length: 20 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
    
    default:
      // Default format: XXXXX-XXXXX-XXXXX
      return Array.from({ length: 3 }, () => 
        Array.from({ length: 5 }, () => chars[Math.floor(Math.random() * chars.length)]).join('')
      ).join('-');
  }
}

// Run the seed function
seed();
