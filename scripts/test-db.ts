#!/usr/bin/env tsx

/**
 * Database Connection Test
 * 
 * This script tests the database connection without running migrations.
 */

import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...\n');

  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.log('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  console.log('📋 Database URL format check:');
  console.log(`URL: ${databaseUrl.substring(0, 50)}...`);
  
  // Parse the URL to check format
  try {
    const url = new URL(databaseUrl);
    console.log('✅ URL format is valid');
    console.log(`   Protocol: ${url.protocol}`);
    console.log(`   Host: ${url.hostname}`);
    console.log(`   Port: ${url.port}`);
    console.log(`   Database: ${url.pathname}`);
    console.log(`   Username: ${url.username}`);
    console.log(`   Password: ${url.password ? '[HIDDEN]' : 'NOT SET'}`);
  } catch (error) {
    console.log('❌ Invalid URL format:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }

  // Try to connect using postgres directly
  try {
    const { default: postgres } = await import('postgres');
    
    console.log('\n🔌 Attempting database connection...');
    
    const sql = postgres(databaseUrl, {
      max: 1,
      idle_timeout: 5,
      connect_timeout: 10,
    });

    // Test query
    const result = await sql`SELECT version()`;
    console.log('✅ Database connection successful!');
    console.log(`   PostgreSQL version: ${result[0].version.split(' ')[0]} ${result[0].version.split(' ')[1]}`);
    
    await sql.end();
    
  } catch (error) {
    console.log('❌ Database connection failed:');
    console.log(`   Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    
    if (error instanceof Error) {
      if (error.message.includes('password authentication failed')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('   1. Check if your database password is correct');
        console.log('   2. Try resetting your database password in Supabase dashboard');
        console.log('   3. Make sure special characters in password are URL-encoded');
        console.log('   4. Run: pnpm encode-password "your-actual-password"');
      } else if (error.message.includes('timeout')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('   1. Check your internet connection');
        console.log('   2. Verify the database host is correct');
        console.log('   3. Check if your IP is allowed in Supabase settings');
      }
    }
    
    process.exit(1);
  }

  console.log('\n🎉 Database connection test completed successfully!');
}

testDatabaseConnection().catch(console.error);
