#!/usr/bin/env tsx

/**
 * Database Password URL Encoder
 * 
 * This script helps encode database passwords that contain special characters
 * for use in PostgreSQL connection URLs.
 */

function encodePassword(password: string): string {
  // URL encode the password to handle special characters
  return encodeURIComponent(password);
}

function createDatabaseUrl(password: string, projectId: string = 'cvoecjjubueppybpnswz'): string {
  const encodedPassword = encodePassword(password);
  return `postgresql://postgres:${encodedPassword}@db.${projectId}.supabase.co:5432/postgres`;
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('🔐 Database Password URL Encoder');
    console.log('');
    console.log('Usage: tsx scripts/encode-password.ts "your-password"');
    console.log('');
    console.log('Example:');
    console.log('  tsx scripts/encode-password.ts "myP@ssw0rd!"');
    console.log('');
    console.log('This will output the properly encoded DATABASE_URL for your .env.local file.');
    process.exit(1);
  }

  const password = args[0];
  const projectId = args[1] || 'cvoecjjubueppybpnswz';
  
  console.log('🔐 Database Password URL Encoder');
  console.log('');
  console.log('Original password:', password);
  console.log('Encoded password:', encodePassword(password));
  console.log('');
  console.log('📋 Copy this to your .env.local file:');
  console.log('');
  console.log(`DATABASE_URL=${createDatabaseUrl(password, projectId)}`);
  console.log('');
  
  // Check for common problematic characters
  const problematicChars = ['&', '?', '#', '/', '@', ':', ';', '=', '+', ' ', '%'];
  const foundChars = problematicChars.filter(char => password.includes(char));
  
  if (foundChars.length > 0) {
    console.log('⚠️  Special characters detected:', foundChars.join(', '));
    console.log('   These have been properly encoded in the URL above.');
  } else {
    console.log('✅ No special characters detected - encoding not strictly necessary but applied for safety.');
  }
  
  console.log('');
  console.log('💡 Tip: If you continue to have connection issues, try:');
  console.log('   1. Resetting your database password in Supabase dashboard');
  console.log('   2. Using a simpler password without special characters');
  console.log('   3. Running: pnpm check-env');
}

main();
