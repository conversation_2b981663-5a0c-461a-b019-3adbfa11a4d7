-- Supabase Storage Setup Script
-- 在 Supabase Dashboard > SQL Editor 中运行此脚本

-- 1. 创建存储桶（如果不存在）
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'games.source', 
  'games.source', 
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'video/mp4', 'video/webm', 'video/mov']
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 2. 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Allow authenticated users to upload" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete" ON storage.objects;

-- 3. 创建上传策略
CREATE POLICY "Allow authenticated users to upload" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'games.source' 
  AND auth.role() = 'authenticated'
);

-- 4. 创建读取策略（公开访问）
CREATE POLICY "Allow public read access" ON storage.objects
FOR SELECT USING (bucket_id = 'games.source');

-- 5. 创建更新策略
CREATE POLICY "Allow authenticated users to update" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'games.source' 
  AND auth.role() = 'authenticated'
);

-- 6. 创建删除策略
CREATE POLICY "Allow authenticated users to delete" ON storage.objects
FOR DELETE USING (
  bucket_id = 'games.source' 
  AND auth.role() = 'authenticated'
);

-- 7. 验证配置
SELECT 
  'Bucket created successfully' as status,
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets 
WHERE id = 'games.source';

-- 8. 显示策略
SELECT 
  'Policies created successfully' as status,
  schemaname,
  tablename,
  policyname,
  cmd,
  qual
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%games.source%' OR policyname LIKE '%authenticated%' OR policyname LIKE '%public%';

-- 完成提示
SELECT 'Storage setup completed! You can now upload files to the games.source bucket.' as message;
