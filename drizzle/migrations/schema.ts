import { pgTable, index, foreignKey, uuid, text, timestamp, jsonb, unique, boolean, integer, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const activationCodeStatus = pgEnum("activation_code_status", ['unused', 'locked', 'used', 'invalid'])
export const actorType = pgEnum("actor_type", ['user', 'admin', 'system'])
export const afterSaleStatus = pgEnum("after_sale_status", ['pending', 'resolved', 'rejected'])
export const orderStatus = pgEnum("order_status", ['pending', 'paid', 'delivered', 'refunded', 'under_review'])
export const platform = pgEnum("platform", ['switch', 'ps', 'xbox', 'steam', 'gift_card'])
export const riskLevel = pgEnum("risk_level", ['low', 'medium', 'high'])
export const userRole = pgEnum("user_role", ['user', 'admin', 'super_admin'])


export const afterSales = pgTable("after_sales", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	orderId: uuid("order_id").notNull(),
	issueType: text("issue_type").notNull(),
	description: text(),
	status: afterSaleStatus().default('pending').notNull(),
	resolution: text(),
	evidenceUrls: text("evidence_urls").array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("after_sales_order_idx").using("btree", table.orderId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.orderId],
			foreignColumns: [orders.id],
			name: "after_sales_order_id_orders_id_fk"
		}).onDelete("cascade"),
]);

export const auditLogs = pgTable("audit_logs", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	actorId: uuid("actor_id"),
	actorType: actorType("actor_type").notNull(),
	action: text().notNull(),
	details: jsonb().default({}).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
});

export const orderDeliveries = pgTable("order_deliveries", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	orderId: uuid("order_id").notNull(),
	activationCodeId: uuid("activation_code_id").notNull(),
	deliveredAt: timestamp("delivered_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.activationCodeId],
			foreignColumns: [activationCodes.id],
			name: "order_deliveries_activation_code_id_activation_codes_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.orderId],
			foreignColumns: [orders.id],
			name: "order_deliveries_order_id_orders_id_fk"
		}).onDelete("cascade"),
	unique("order_deliveries_order_unique").on(table.orderId),
]);

export const users = pgTable("users", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	email: text().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	role: userRole().default('user').notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	lastLoginAt: timestamp("last_login_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("users_email_idx").using("btree", table.email.asc().nullsLast().op("text_ops")),
	index("users_role_idx").using("btree", table.role.asc().nullsLast().op("enum_ops")),
	unique("users_email_unique").on(table.email),
]);

export const products = pgTable("products", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	platform: platform().notNull(),
	region: text().notNull(),
	currency: text().default('USD').notNull(),
	amountCents: integer("amount_cents").notNull(),
	description: text(),
	isActive: boolean("is_active").default(true).notNull(),
	slug: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("products_name_idx").using("btree", table.name.asc().nullsLast().op("text_ops")),
	index("products_platform_idx").using("btree", table.platform.asc().nullsLast().op("enum_ops")),
	unique("products_slug_unique").on(table.slug),
]);

export const activationCodes = pgTable("activation_codes", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	productId: uuid("product_id").notNull(),
	code: text().notNull(),
	status: activationCodeStatus().default('unused').notNull(),
	lockedByOrderId: uuid("locked_by_order_id"),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }),
	deliveredAt: timestamp("delivered_at", { withTimezone: true, mode: 'string' }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("activation_codes_product_status_idx").using("btree", table.productId.asc().nullsLast().op("uuid_ops"), table.status.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.lockedByOrderId],
			foreignColumns: [orders.id],
			name: "activation_codes_locked_by_order_id_orders_id_fk"
		}),
	foreignKey({
			columns: [table.productId],
			foreignColumns: [products.id],
			name: "activation_codes_product_id_products_id_fk"
		}).onDelete("cascade"),
	unique("activation_codes_code_unique").on(table.code),
]);

export const orders = pgTable("orders", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	productId: uuid("product_id").notNull(),
	currency: text().notNull(),
	amountCents: integer("amount_cents").notNull(),
	status: orderStatus().default('pending').notNull(),
	paidAt: timestamp("paid_at", { withTimezone: true, mode: 'string' }),
	deliveredAt: timestamp("delivered_at", { withTimezone: true, mode: 'string' }),
	refundedAt: timestamp("refunded_at", { withTimezone: true, mode: 'string' }),
	paymentIntentId: text("payment_intent_id").notNull(),
	riskLevel: riskLevel("risk_level").default('low').notNull(),
	metadata: jsonb().default({}).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("orders_status_idx").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	index("orders_user_created_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops"), table.createdAt.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.productId],
			foreignColumns: [products.id],
			name: "orders_product_id_products_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "orders_user_id_users_id_fk"
		}).onDelete("cascade"),
	unique("orders_payment_intent_id_unique").on(table.paymentIntentId),
]);
