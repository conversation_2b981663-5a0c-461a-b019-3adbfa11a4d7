-- Migration: Add missing fields to products table
-- Created: 2025-01-08

-- First, create the product category enum if it doesn't exist
DO $$ BEGIN
    CREATE TYPE product_category AS ENUM ('game', 'gift_card', 'software', 'dlc');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add missing columns to products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS category product_category,
ADD COLUMN IF NOT EXISTS detail_content TEXT,
ADD COLUMN IF NOT EXISTS main_image_url TEXT,
ADD COLUMN IF NOT EXISTS video_url TEXT,
ADD COLUMN IF NOT EXISTS image_urls TEXT[] DEFAULT ARRAY[]::TEXT[];

-- Set default category for existing products
UPDATE products 
SET category = 'game' 
WHERE category IS NULL;

-- Make category NOT NULL after setting defaults
ALTER TABLE products 
ALTER COLUMN category SET NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN products.category IS 'Product category (game, gift_card, software, dlc)';
COMMENT ON COLUMN products.detail_content IS 'Rich text content for product details page';
COMMENT ON COLUMN products.main_image_url IS 'Main product image URL (primary display image)';
COMMENT ON COLUMN products.video_url IS 'Product video URL for enhanced media display';
COMMENT ON COLUMN products.image_urls IS 'Array of additional product image URLs for gallery';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS products_category_idx ON products(category);
CREATE INDEX IF NOT EXISTS products_main_image_url_idx ON products(main_image_url) WHERE main_image_url IS NOT NULL;
CREATE INDEX IF NOT EXISTS products_video_url_idx ON products(video_url) WHERE video_url IS NOT NULL;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
AND table_schema = 'public'
ORDER BY ordinal_position;
