{"id": "e5a7339e-c18a-45ae-8151-8208b28f1b40", "prevId": "82ace42c-9aa3-47ef-8782-0dda70e37d34", "version": "7", "dialect": "postgresql", "tables": {"public.activation_codes": {"name": "activation_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "activation_code_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'unused'"}, "locked_by_order_id": {"name": "locked_by_order_id", "type": "uuid", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "delivered_at": {"name": "delivered_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"activation_codes_product_status_idx": {"name": "activation_codes_product_status_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"activation_codes_product_id_products_id_fk": {"name": "activation_codes_product_id_products_id_fk", "tableFrom": "activation_codes", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "activation_codes_locked_by_order_id_orders_id_fk": {"name": "activation_codes_locked_by_order_id_orders_id_fk", "tableFrom": "activation_codes", "tableTo": "orders", "columnsFrom": ["locked_by_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"activation_codes_code_unique": {"name": "activation_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.after_sales": {"name": "after_sales", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "issue_type": {"name": "issue_type", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "after_sale_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "resolution": {"name": "resolution", "type": "text", "primaryKey": false, "notNull": false}, "evidence_urls": {"name": "evidence_urls", "type": "text[]", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"after_sales_order_idx": {"name": "after_sales_order_idx", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"after_sales_order_id_orders_id_fk": {"name": "after_sales_order_id_orders_id_fk", "tableFrom": "after_sales", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "actor_id": {"name": "actor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "actor_type": {"name": "actor_type", "type": "actor_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_deliveries": {"name": "order_deliveries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "activation_code_id": {"name": "activation_code_id", "type": "uuid", "primaryKey": false, "notNull": true}, "delivered_at": {"name": "delivered_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"order_deliveries_order_id_orders_id_fk": {"name": "order_deliveries_order_id_orders_id_fk", "tableFrom": "order_deliveries", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_deliveries_activation_code_id_activation_codes_id_fk": {"name": "order_deliveries_activation_code_id_activation_codes_id_fk", "tableFrom": "order_deliveries", "tableTo": "activation_codes", "columnsFrom": ["activation_code_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"order_deliveries_order_unique": {"name": "order_deliveries_order_unique", "nullsNotDistinct": false, "columns": ["order_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "amount_cents": {"name": "amount_cents", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "order_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "paid_at": {"name": "paid_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "delivered_at": {"name": "delivered_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "refunded_at": {"name": "refunded_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "payment_intent_id": {"name": "payment_intent_id", "type": "text", "primaryKey": false, "notNull": true}, "risk_level": {"name": "risk_level", "type": "risk_level", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'low'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"orders_user_created_idx": {"name": "orders_user_created_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_status_idx": {"name": "orders_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_user_id_users_id_fk": {"name": "orders_user_id_users_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_product_id_products_id_fk": {"name": "orders_product_id_products_id_fk", "tableFrom": "orders", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_payment_intent_id_unique": {"name": "orders_payment_intent_id_unique", "nullsNotDistinct": false, "columns": ["payment_intent_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "platform", "typeSchema": "public", "primaryKey": false, "notNull": true}, "region": {"name": "region", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "amount_cents": {"name": "amount_cents", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"products_name_idx": {"name": "products_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_platform_idx": {"name": "products_platform_idx", "columns": [{"expression": "platform", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"products_slug_unique": {"name": "products_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.activation_code_status": {"name": "activation_code_status", "schema": "public", "values": ["unused", "locked", "used", "invalid"]}, "public.actor_type": {"name": "actor_type", "schema": "public", "values": ["user", "admin", "system"]}, "public.after_sale_status": {"name": "after_sale_status", "schema": "public", "values": ["pending", "resolved", "rejected"]}, "public.order_status": {"name": "order_status", "schema": "public", "values": ["pending", "paid", "delivered", "refunded", "under_review"]}, "public.platform": {"name": "platform", "schema": "public", "values": ["switch", "ps", "xbox", "steam", "gift_card"]}, "public.risk_level": {"name": "risk_level", "schema": "public", "values": ["low", "medium", "high"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["user", "admin", "super_admin"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}