import { relations } from "drizzle-orm/relations";
import { orders, afterSales, activationCodes, orderDeliveries, products, users } from "./schema";

export const afterSalesRelations = relations(afterSales, ({one}) => ({
	order: one(orders, {
		fields: [afterSales.orderId],
		references: [orders.id]
	}),
}));

export const ordersRelations = relations(orders, ({one, many}) => ({
	afterSales: many(afterSales),
	orderDeliveries: many(orderDeliveries),
	activationCodes: many(activationCodes),
	product: one(products, {
		fields: [orders.productId],
		references: [products.id]
	}),
	user: one(users, {
		fields: [orders.userId],
		references: [users.id]
	}),
}));

export const orderDeliveriesRelations = relations(orderDeliveries, ({one}) => ({
	activationCode: one(activationCodes, {
		fields: [orderDeliveries.activationCodeId],
		references: [activationCodes.id]
	}),
	order: one(orders, {
		fields: [orderDeliveries.orderId],
		references: [orders.id]
	}),
}));

export const activationCodesRelations = relations(activationCodes, ({one, many}) => ({
	orderDeliveries: many(orderDeliveries),
	order: one(orders, {
		fields: [activationCodes.lockedByOrderId],
		references: [orders.id]
	}),
	product: one(products, {
		fields: [activationCodes.productId],
		references: [products.id]
	}),
}));

export const productsRelations = relations(products, ({many}) => ({
	activationCodes: many(activationCodes),
	orders: many(orders),
}));

export const usersRelations = relations(users, ({many}) => ({
	orders: many(orders),
}));