-- Migration: Add media and rich content fields to products table
-- Created: 2025-01-08

-- Add new fields for enhanced product content
ALTER TABLE products 
ADD COLUMN detail_content TEXT,
ADD COLUMN main_image_url TEXT,
ADD COLUMN video_url TEXT,
ADD COLUMN image_urls TEXT[] DEFAULT ARRAY[]::TEXT[];

-- Add comments for documentation
COMMENT ON COLUMN products.detail_content IS 'Rich text content for product details page';
COMMENT ON COLUMN products.main_image_url IS 'Main product image URL (primary display image)';
COMMENT ON COLUMN products.video_url IS 'Product video URL for enhanced media display';
COMMENT ON COLUMN products.image_urls IS 'Array of additional product image URLs for gallery';

-- Create index for better query performance on media fields
CREATE INDEX IF NOT EXISTS products_main_image_url_idx ON products(main_image_url) WHERE main_image_url IS NOT NULL;
CREATE INDEX IF NOT EXISTS products_video_url_idx ON products(video_url) WHERE video_url IS NOT NULL;
