'use client';

import { useState, useEffect } from 'react';

export interface ProductOption {
  value: string;
  label: string;
}

export interface ProductOptions {
  platforms: ProductOption[];
  categories: ProductOption[];
  statuses: ProductOption[];
  currencies: ProductOption[];
  regions: ProductOption[];
}

export function useProductOptions() {
  const [options, setOptions] = useState<ProductOptions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/product-options');
        
        if (!response.ok) {
          throw new Error('Failed to fetch product options');
        }

        const result = await response.json();
        
        if (result.success) {
          setOptions(result.data);
        } else {
          throw new Error(result.error || 'Failed to fetch product options');
        }
      } catch (err) {
        console.error('Error fetching product options:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchOptions();
  }, []);

  return { options, loading, error };
}
