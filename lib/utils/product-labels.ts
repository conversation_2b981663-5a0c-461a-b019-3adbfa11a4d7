// Product label mappings - centralized for consistency
export const statusLabels = {
  draft: '草稿',
  active: '在售',
  inactive: '下架',
} as const;

export const platformLabels = {
  steam: 'Steam',
  epic: 'Epic Games',
  origin: 'Origin',
  uplay: 'Uplay',
  battlenet: 'Battle.net',
  gog: 'GOG',
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  gift_card: 'Gift Card',
} as const;

export const categoryLabels = {
  action: '动作游戏',
  adventure: '冒险游戏',
  rpg: '角色扮演',
  strategy: '策略游戏',
  simulation: '模拟游戏',
  sports: '体育游戏',
  racing: '竞速游戏',
  puzzle: '益智游戏',
  indie: '独立游戏',
  horror: '恐怖游戏',
  fighting: '格斗游戏',
  shooter: '射击游戏',
} as const;

export const statusConfig = {
  draft: {
    label: statusLabels.draft,
    color: 'bg-gray-100 text-gray-800',
  },
  active: {
    label: statusLabels.active,
    color: 'bg-green-100 text-green-800',
  },
  inactive: {
    label: statusLabels.inactive,
    color: 'bg-red-100 text-red-800',
  },
} as const;

export const priorityConfig = {
  low: { label: '低', color: 'bg-gray-100 text-gray-800' },
  medium: { label: '中', color: 'bg-yellow-100 text-yellow-800' },
  high: { label: '高', color: 'bg-red-100 text-red-800' },
} as const;
