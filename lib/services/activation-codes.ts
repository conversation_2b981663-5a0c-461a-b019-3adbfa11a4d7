import { db } from '@/lib/db';
import { activationCodes, orderDeliveries, auditLogs, products } from '@/lib/db/schema';
import { eq, and, sql } from 'drizzle-orm';
import type { SelectActivationCode, InsertActivationCode } from '@/lib/db/schema';

export interface DeliveryResult {
  success: boolean;
  activationCode?: SelectActivationCode;
  error?: string;
}

/**
 * Allocate and deliver an activation code to an order
 * Uses FOR UPDATE SKIP LOCKED to prevent race conditions
 */
export async function allocateActivationCode(
  orderId: string,
  productId: string,
  actorId?: string
): Promise<DeliveryResult> {
  try {
    // Start a transaction to ensure atomicity
    const result = await db.transaction(async (tx) => {
      // Find and lock an unused activation code for this product
      // Using FOR UPDATE SKIP LOCKED to prevent deadlocks
      const availableCodes = await tx
        .select()
        .from(activationCodes)
        .where(
          and(
            eq(activationCodes.productId, productId),
            eq(activationCodes.status, 'unused')
          )
        )
        .for('update', { skipLocked: true })
        .limit(1);

      if (availableCodes.length === 0) {
        throw new Error('No available activation codes for this product');
      }

      const code = availableCodes[0];

      // Update the activation code status to 'used' and lock it to this order
      const updatedCode = await tx
        .update(activationCodes)
        .set({
          status: 'used',
          lockedByOrderId: orderId,
          deliveredAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(activationCodes.id, code.id))
        .returning();

      if (updatedCode.length === 0) {
        throw new Error('Failed to update activation code');
      }

      // Create order delivery record
      await tx.insert(orderDeliveries).values({
        orderId,
        activationCodeId: code.id,
        deliveredAt: new Date(),
      });

      // Log the delivery
      await tx.insert(auditLogs).values({
        actorId,
        actorType: 'system',
        action: 'activation_code_delivered',
        details: {
          orderId,
          activationCodeId: code.id,
          productId,
          timestamp: new Date().toISOString(),
        },
      });

      return updatedCode[0];
    });

    return {
      success: true,
      activationCode: result,
    };
  } catch (error) {
    console.error('Failed to allocate activation code:', error);
    
    // Log the failure
    await db.insert(auditLogs).values({
      actorId,
      actorType: 'system',
      action: 'activation_code_delivery_failed',
      details: {
        orderId,
        productId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Add new activation codes for a product
 */
export async function addActivationCodes(
  productId: string,
  codes: string[],
  actorId?: string
): Promise<SelectActivationCode[]> {
  const codeData: InsertActivationCode[] = codes.map(code => ({
    productId,
    code,
    status: 'unused' as const,
  }));

  const result = await db.insert(activationCodes).values(codeData).returning();

  // Log the addition
  await db.insert(auditLogs).values({
    actorId,
    actorType: 'admin',
    action: 'activation_codes_added',
    details: {
      productId,
      codesCount: codes.length,
      timestamp: new Date().toISOString(),
    },
  });

  return result;
}

/**
 * Get activation code stock count for a product
 */
export async function getStockCount(productId: string): Promise<number> {
  const result = await db
    .select({
      count: sql<number>`COUNT(*)`,
    })
    .from(activationCodes)
    .where(
      and(
        eq(activationCodes.productId, productId),
        eq(activationCodes.status, 'unused')
      )
    );

  return result[0]?.count || 0;
}

/**
 * Get activation codes for a product with status filter
 */
export async function getActivationCodes(
  productId: string,
  status?: 'unused' | 'locked' | 'used' | 'invalid',
  limit: number = 50,
  offset: number = 0
): Promise<SelectActivationCode[]> {
  const conditions = [eq(activationCodes.productId, productId)];

  if (status) {
    conditions.push(eq(activationCodes.status, status));
  }

  return await db
    .select()
    .from(activationCodes)
    .where(and(...conditions))
    .limit(limit)
    .offset(offset)
    .orderBy(activationCodes.createdAt);
}

/**
 * Mark activation code as invalid
 */
export async function markCodeAsInvalid(
  codeId: string,
  actorId?: string,
  reason?: string
): Promise<SelectActivationCode | null> {
  const result = await db
    .update(activationCodes)
    .set({
      status: 'invalid',
      updatedAt: new Date(),
    })
    .where(eq(activationCodes.id, codeId))
    .returning();

  if (result.length > 0) {
    // Log the invalidation
    await db.insert(auditLogs).values({
      actorId,
      actorType: 'admin',
      action: 'activation_code_invalidated',
      details: {
        activationCodeId: codeId,
        reason: reason || 'No reason provided',
        timestamp: new Date().toISOString(),
      },
    });
  }

  return result[0] || null;
}

/**
 * Get activation code by order ID
 */
export async function getActivationCodeByOrderId(orderId: string): Promise<SelectActivationCode | null> {
  const result = await db
    .select({
      id: activationCodes.id,
      productId: activationCodes.productId,
      code: activationCodes.code,
      status: activationCodes.status,
      lockedByOrderId: activationCodes.lockedByOrderId,
      expiresAt: activationCodes.expiresAt,
      deliveredAt: activationCodes.deliveredAt,
      createdAt: activationCodes.createdAt,
      updatedAt: activationCodes.updatedAt,
    })
    .from(activationCodes)
    .innerJoin(orderDeliveries, eq(activationCodes.id, orderDeliveries.activationCodeId))
    .where(eq(orderDeliveries.orderId, orderId))
    .limit(1);

  return result[0] || null;
}

/**
 * Check if product has sufficient stock
 */
export async function hasStock(productId: string, quantity: number = 1): Promise<boolean> {
  const stockCount = await getStockCount(productId);
  return stockCount >= quantity;
}

/**
 * Get low stock products (less than or equal to threshold, only active products)
 */
export async function getLowStockProducts(threshold: number = 10): Promise<Array<{
  productId: string;
  stockCount: number;
}>> {
  const result = await db
    .select({
      productId: activationCodes.productId,
      stockCount: sql<number>`COUNT(*)`,
    })
    .from(activationCodes)
    .innerJoin(products, eq(activationCodes.productId, products.id))
    .where(and(
      eq(activationCodes.status, 'unused'),
      eq(products.isActive, true)
    ))
    .groupBy(activationCodes.productId)
    .having(sql`COUNT(*) <= ${threshold}`);

  return result;
}
