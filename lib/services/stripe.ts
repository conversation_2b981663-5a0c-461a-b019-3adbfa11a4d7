import Stripe from 'stripe';

// Only initialize <PERSON><PERSON> on the server side and when the key is available
let stripe: Stripe | null = null;

if (typeof window === 'undefined' && process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2025-07-30.basil',
  });
}

export { stripe };

export interface CreatePaymentIntentData {
  amount: number; // in cents
  currency: string;
  productId: string;
  userId: string;
  userEmail: string;
  metadata?: Record<string, string>;
}

export interface RiskAssessmentResult {
  riskLevel: 'low' | 'medium' | 'high';
  riskScore: number;
  reasons: string[];
}

/**
 * Create a Stripe Payment Intent
 */
export async function createPaymentIntent(data: CreatePaymentIntentData): Promise<Stripe.PaymentIntent> {
  if (!stripe) {
    throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.');
  }

  const paymentIntent = await stripe.paymentIntents.create({
    amount: data.amount,
    currency: data.currency.toLowerCase(),
    metadata: {
      productId: data.productId,
      userId: data.userId,
      userEmail: data.userEmail,
      ...data.metadata,
    },
    // Enable automatic payment methods
    automatic_payment_methods: {
      enabled: true,
    },
    // Set up for future usage if needed
    setup_future_usage: 'off_session',
  });

  return paymentIntent;
}

/**
 * Retrieve a Payment Intent
 */
export async function getPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }
  return await stripe.paymentIntents.retrieve(paymentIntentId);
}

/**
 * Confirm a Payment Intent
 */
export async function confirmPaymentIntent(
  paymentIntentId: string,
  paymentMethodId: string
): Promise<Stripe.PaymentIntent> {
  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }
  return await stripe.paymentIntents.confirm(paymentIntentId, {
    payment_method: paymentMethodId,
  });
}

/**
 * Cancel a Payment Intent
 */
export async function cancelPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }
  return await stripe.paymentIntents.cancel(paymentIntentId);
}

/**
 * Create a refund
 */
export async function createRefund(
  paymentIntentId: string,
  amount?: number,
  reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer'
): Promise<Stripe.Refund> {
  const refundData: Stripe.RefundCreateParams = {
    payment_intent: paymentIntentId,
  };

  if (amount) {
    refundData.amount = amount;
  }

  if (reason) {
    refundData.reason = reason;
  }

  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }
  return await stripe.refunds.create(refundData);
}

/**
 * Assess payment risk using Stripe Radar data and custom rules
 */
export async function assessPaymentRisk(paymentIntent: Stripe.PaymentIntent): Promise<RiskAssessmentResult> {
  let riskScore = 0;
  const reasons: string[] = [];

  // Check Stripe Radar risk level
  const charge = paymentIntent.latest_charge as Stripe.Charge;
  if (charge?.outcome?.risk_level) {
    switch (charge.outcome.risk_level) {
      case 'elevated':
        riskScore += 30;
        reasons.push('Stripe Radar: Elevated risk');
        break;
      case 'highest':
        riskScore += 60;
        reasons.push('Stripe Radar: Highest risk');
        break;
    }
  }

  // Check for CVC check failure
  if (charge?.payment_method_details?.card?.checks?.cvc_check === 'fail') {
    riskScore += 20;
    reasons.push('CVC check failed');
  }

  // Check for address verification failure
  if (charge?.payment_method_details?.card?.checks?.address_line1_check === 'fail') {
    riskScore += 15;
    reasons.push('Address verification failed');
  }

  // Check for postal code verification failure
  if (charge?.payment_method_details?.card?.checks?.address_postal_code_check === 'fail') {
    riskScore += 10;
    reasons.push('Postal code verification failed');
  }

  // Check payment method country vs billing country mismatch
  const cardCountry = charge?.payment_method_details?.card?.country;
  const billingCountry = charge?.billing_details?.address?.country;
  if (cardCountry && billingCountry && cardCountry !== billingCountry) {
    riskScore += 25;
    reasons.push('Card country and billing country mismatch');
  }

  // Check for high-risk countries (customize based on your business)
  const highRiskCountries = ['CN', 'RU', 'NG', 'PK']; // Example list
  if (cardCountry && highRiskCountries.includes(cardCountry)) {
    riskScore += 20;
    reasons.push(`High-risk country: ${cardCountry}`);
  }

  // Check for unusual transaction amounts
  const amount = paymentIntent.amount;
  if (amount > 50000) { // $500+
    riskScore += 15;
    reasons.push('High transaction amount');
  }

  // Determine risk level based on score
  let riskLevel: 'low' | 'medium' | 'high';
  if (riskScore >= 50) {
    riskLevel = 'high';
  } else if (riskScore >= 25) {
    riskLevel = 'medium';
  } else {
    riskLevel = 'low';
  }

  return {
    riskLevel,
    riskScore,
    reasons,
  };
}

/**
 * Verify webhook signature
 */
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string,
  secret: string
): Stripe.Event {
  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }
  return stripe.webhooks.constructEvent(payload, signature, secret);
}

/**
 * Get customer by email or create new one
 */
export async function getOrCreateCustomer(email: string, name?: string): Promise<Stripe.Customer> {
  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }

  // Search for existing customer
  const existingCustomers = await stripe.customers.list({
    email,
    limit: 1,
  });

  if (existingCustomers.data.length > 0) {
    return existingCustomers.data[0];
  }

  // Create new customer
  return await stripe.customers.create({
    email,
    name,
  });
}

/**
 * Create a setup intent for saving payment methods
 */
export async function createSetupIntent(customerId: string): Promise<Stripe.SetupIntent> {
  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }
  return await stripe.setupIntents.create({
    customer: customerId,
    payment_method_types: ['card'],
    usage: 'off_session',
  });
}

/**
 * List customer's payment methods
 */
export async function getCustomerPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }

  const paymentMethods = await stripe.paymentMethods.list({
    customer: customerId,
    type: 'card',
  });

  return paymentMethods.data;
}

/**
 * Format amount for display (convert cents to dollars)
 */
export function formatAmount(amountCents: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amountCents / 100);
}

/**
 * Convert dollars to cents
 */
export function dollarsToCents(dollars: number): number {
  return Math.round(dollars * 100);
}

/**
 * Convert cents to dollars
 */
export function centsToDollars(cents: number): number {
  return cents / 100;
}
