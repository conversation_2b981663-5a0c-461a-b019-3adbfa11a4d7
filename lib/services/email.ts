import { Resend } from 'resend';
import type { SelectOrder, SelectProduct, SelectActivationCode } from '@/lib/db/schema';

if (!process.env.RESEND_API_KEY) {
  throw new Error('Missing RESEND_API_KEY environment variable');
}

if (!process.env.FROM_EMAIL) {
  throw new Error('Missing FROM_EMAIL environment variable');
}

const resend = new Resend(process.env.RESEND_API_KEY);
const FROM_EMAIL = process.env.FROM_EMAIL;
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

export interface OrderConfirmationEmailData {
  order: SelectOrder;
  product: SelectProduct;
  userEmail: string;
  activationCode?: SelectActivationCode;
}

export interface OrderReceiptEmailData {
  order: SelectOrder;
  product: SelectProduct;
  userEmail: string;
}

/**
 * Send order confirmation email with activation code
 */
export async function sendOrderConfirmationEmail(data: OrderConfirmationEmailData) {
  const { order, product, userEmail, activationCode } = data;
  
  const subject = `Your ${product.name} activation code is ready!`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; }
        .content { padding: 20px 0; }
        .activation-code { 
          background: #e3f2fd; 
          padding: 20px; 
          border-radius: 8px; 
          text-align: center; 
          margin: 20px 0;
          border-left: 4px solid #2196f3;
        }
        .code { 
          font-family: 'Courier New', monospace; 
          font-size: 18px; 
          font-weight: bold; 
          color: #1976d2;
          background: white;
          padding: 10px;
          border-radius: 4px;
          display: inline-block;
          margin: 10px 0;
        }
        .order-details { background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        .button { 
          display: inline-block; 
          padding: 12px 24px; 
          background: #2196f3; 
          color: white; 
          text-decoration: none; 
          border-radius: 4px; 
          margin: 10px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎮 Your Digital Game Code is Ready!</h1>
        </div>
        
        <div class="content">
          <p>Hi there!</p>
          
          <p>Great news! Your order has been processed and your activation code is ready to use.</p>
          
          ${activationCode ? `
          <div class="activation-code">
            <h3>Your Activation Code</h3>
            <div class="code">${activationCode.code}</div>
            <p><strong>Important:</strong> Keep this code safe and don't share it with anyone.</p>
          </div>
          ` : ''}
          
          <div class="order-details">
            <h3>Order Details</h3>
            <p><strong>Product:</strong> ${product.name}</p>
            <p><strong>Platform:</strong> ${product.platform.toUpperCase()}</p>
            <p><strong>Region:</strong> ${product.region}</p>
            <p><strong>Order ID:</strong> ${order.id}</p>
            <p><strong>Amount:</strong> ${formatCurrency(order.amountCents, order.currency)}</p>
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
          </div>
          
          <p>You can also view your order and download your code anytime from your account:</p>
          
          <a href="${APP_URL}/orders/${order.id}" class="button">View Order Details</a>
          
          <h3>How to Redeem Your Code</h3>
          <p>Follow these steps to redeem your activation code:</p>
          <ol>
            <li>Open your ${product.platform.toUpperCase()} platform</li>
            <li>Go to the redeem code section</li>
            <li>Enter your activation code: <strong>${activationCode?.code || '[Code will be provided]'}</strong></li>
            <li>Follow the on-screen instructions to complete the redemption</li>
          </ol>
          
          <p>If you have any issues with your code or need assistance, please don't hesitate to contact our support team.</p>
        </div>
        
        <div class="footer">
          <p>Thank you for your purchase!</p>
          <p>If you have any questions, reply to this email or visit our support center.</p>
          <p><a href="${APP_URL}">Visit our website</a></p>
        </div>
      </div>
    </body>
    </html>
  `;

  try {
    const result = await resend.emails.send({
      from: FROM_EMAIL,
      to: userEmail,
      subject,
      html,
    });

    return { success: true, messageId: result.data?.id };
  } catch (error) {
    console.error('Failed to send order confirmation email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Send order receipt email (without activation code)
 */
export async function sendOrderReceiptEmail(data: OrderReceiptEmailData) {
  const { order, product, userEmail } = data;
  
  const subject = `Order Receipt - ${product.name}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; }
        .content { padding: 20px 0; }
        .order-details { background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        .button { 
          display: inline-block; 
          padding: 12px 24px; 
          background: #2196f3; 
          color: white; 
          text-decoration: none; 
          border-radius: 4px; 
          margin: 10px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📧 Order Receipt</h1>
        </div>
        
        <div class="content">
          <p>Hi there!</p>
          
          <p>Thank you for your order! We've received your payment and are processing your request.</p>
          
          <div class="order-details">
            <h3>Order Details</h3>
            <p><strong>Product:</strong> ${product.name}</p>
            <p><strong>Platform:</strong> ${product.platform.toUpperCase()}</p>
            <p><strong>Region:</strong> ${product.region}</p>
            <p><strong>Order ID:</strong> ${order.id}</p>
            <p><strong>Amount:</strong> ${formatCurrency(order.amountCents, order.currency)}</p>
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
            <p><strong>Status:</strong> ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}</p>
          </div>
          
          <p>Your activation code will be delivered shortly. You'll receive another email once it's ready.</p>
          
          <a href="${APP_URL}/orders/${order.id}" class="button">View Order Status</a>
        </div>
        
        <div class="footer">
          <p>Thank you for your purchase!</p>
          <p>If you have any questions, reply to this email or visit our support center.</p>
          <p><a href="${APP_URL}">Visit our website</a></p>
        </div>
      </div>
    </body>
    </html>
  `;

  try {
    const result = await resend.emails.send({
      from: FROM_EMAIL,
      to: userEmail,
      subject,
      html,
    });

    return { success: true, messageId: result.data?.id };
  } catch (error) {
    console.error('Failed to send order receipt email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Send order under review notification
 */
export async function sendOrderUnderReviewEmail(data: OrderReceiptEmailData) {
  const { order, product, userEmail } = data;
  
  const subject = `Order Under Review - ${product.name}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #fff3cd; padding: 20px; text-align: center; border-radius: 8px; border-left: 4px solid #ffc107; }
        .content { padding: 20px 0; }
        .order-details { background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>⏳ Order Under Review</h1>
        </div>
        
        <div class="content">
          <p>Hi there!</p>
          
          <p>Your recent order is currently under review for security purposes. This is a standard procedure to ensure the safety of all transactions.</p>
          
          <div class="order-details">
            <h3>Order Details</h3>
            <p><strong>Product:</strong> ${product.name}</p>
            <p><strong>Order ID:</strong> ${order.id}</p>
            <p><strong>Amount:</strong> ${formatCurrency(order.amountCents, order.currency)}</p>
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
          </div>
          
          <p><strong>What happens next?</strong></p>
          <ul>
            <li>Our team will review your order within 24 hours</li>
            <li>You'll receive an email once the review is complete</li>
            <li>If approved, your activation code will be delivered immediately</li>
          </ul>
          
          <p>We apologize for any inconvenience and appreciate your patience.</p>
        </div>
        
        <div class="footer">
          <p>If you have any questions about this review, please contact our support team.</p>
          <p><a href="${APP_URL}">Visit our website</a></p>
        </div>
      </div>
    </body>
    </html>
  `;

  try {
    const result = await resend.emails.send({
      from: FROM_EMAIL,
      to: userEmail,
      subject,
      html,
    });

    return { success: true, messageId: result.data?.id };
  } catch (error) {
    console.error('Failed to send order under review email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Format currency amount
 */
function formatCurrency(amountCents: number, currency: string): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amountCents / 100);
}
