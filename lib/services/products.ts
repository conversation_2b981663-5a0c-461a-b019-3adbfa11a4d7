import { db } from '@/lib/db';
import { products, activationCodes } from '@/lib/db/schema';
import { eq, and, or, like, sql, desc, asc } from 'drizzle-orm';
import { isFullyConfigured, demoData } from '@/lib/config';
import type { SelectProduct, InsertProduct } from '@/lib/db/schema';

export interface ProductWithStock extends SelectProduct {
  stockCount: number;
}

export interface ProductFilters {
  platform?: string;
  region?: string;
  search?: string;
  status?: 'draft' | 'active' | 'inactive';
}

export interface ProductListOptions {
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'price' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Get all products with stock information
 */
export async function getProductsWithStock(
  filters: ProductFilters = {},
  options: ProductListOptions = {}
): Promise<ProductWithStock[]> {
  // Use demo data if database is not configured
  if (!isFullyConfigured.database) {
    return getDemoProducts(filters, options);
  }

  // Try database query with fallback to demo data
  try {

  const {
    platform,
    region,
    search,
    status = 'active',
  } = filters;

  const {
    limit = 50,
    offset = 0,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = options;

  // Build conditions
  const conditions = [];

  if (status !== undefined) {
    conditions.push(eq(products.status, status));
  }

  if (platform) {
    conditions.push(eq(products.platform, platform as 'switch' | 'ps' | 'xbox' | 'steam' | 'gift_card'));
  }

  if (region) {
    conditions.push(eq(products.region, region));
  }

  if (search) {
    conditions.push(sql`${products.name} ILIKE ${`%${search}%`}`);
  }

  // Build the complete query
  const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

  let query = db
    .select({
      id: products.id,
      name: products.name,
      platform: products.platform,
      category: products.category,
      region: products.region,
      currency: products.currency,
      amountCents: products.amountCents,
      description: products.description,
      status: products.status,
      slug: products.slug,
      imageUrl: products.imageUrl,
      systemRequirements: products.systemRequirements,
      features: products.features,
      tags: products.tags,
      createdAt: products.createdAt,
      updatedAt: products.updatedAt,
      stockCount: sql<number>`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
    })
    .from(products)
    .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
    .$dynamic();

  if (whereClause) {
    query = query.where(whereClause);
  }

  query = query.groupBy(products.id);

  // Apply sorting
  if (sortBy === 'name') {
    query = query.orderBy(sortOrder === 'asc' ? asc(products.name) : desc(products.name));
  } else if (sortBy === 'created_at') {
    query = query.orderBy(sortOrder === 'asc' ? asc(products.createdAt) : desc(products.createdAt));
  } else if (sortBy === 'price') {
    query = query.orderBy(sortOrder === 'asc' ? asc(products.amountCents) : desc(products.amountCents));
  } else {
    query = query.orderBy(desc(products.createdAt)); // default sort
  }

  // Apply pagination
  query = query.limit(limit).offset(offset);

  return await query;

  } catch (error) {
    console.error('Database query failed, falling back to demo data:', error);
    return getDemoProducts(filters, options);
  }
}

/**
 * Get a single product by ID with stock information
 */
export async function getProductById(id: string): Promise<ProductWithStock | null> {
  // Use demo data if database is not configured
  if (!isFullyConfigured.database) {
    return demoData.products.find(p => p.id === id) || null;
  }

  const result = await db
    .select({
      id: products.id,
      name: products.name,
      platform: products.platform,
      category: products.category,
      region: products.region,
      currency: products.currency,
      amountCents: products.amountCents,
      description: products.description,
      detailContent: products.detailContent,
      isActive: products.isActive,
      slug: products.slug,
      // Media fields
      mainImageUrl: products.mainImageUrl,
      videoUrl: products.videoUrl,
      imageUrls: products.imageUrls,
      // Legacy field
      imageUrl: products.imageUrl,
      systemRequirements: products.systemRequirements,
      features: products.features,
      tags: products.tags,
      createdAt: products.createdAt,
      updatedAt: products.updatedAt,
      stockCount: sql<number>`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
    })
    .from(products)
    .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
    .where(eq(products.id, id))
    .groupBy(products.id)
    .limit(1);

  return result[0] || null;
}

/**
 * Get a single product by slug with stock information
 */
export async function getProductBySlug(slug: string): Promise<ProductWithStock | null> {
  // Use demo data if database is not configured
  if (!isFullyConfigured.database) {
    return demoData.products.find(p => p.slug === slug) || null;
  }

  const result = await db
    .select({
      id: products.id,
      name: products.name,
      platform: products.platform,
      category: products.category,
      region: products.region,
      currency: products.currency,
      amountCents: products.amountCents,
      description: products.description,
      detailContent: products.detailContent,
      isActive: products.isActive,
      slug: products.slug,
      // Media fields
      mainImageUrl: products.mainImageUrl,
      videoUrl: products.videoUrl,
      imageUrls: products.imageUrls,
      // Legacy field
      imageUrl: products.imageUrl,
      systemRequirements: products.systemRequirements,
      features: products.features,
      tags: products.tags,
      createdAt: products.createdAt,
      updatedAt: products.updatedAt,
      stockCount: sql<number>`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
    })
    .from(products)
    .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
    .where(eq(products.slug, slug))
    .groupBy(products.id)
    .limit(1);

  return result[0] || null;
}

/**
 * Create a new product
 */
export async function createProduct(productData: InsertProduct): Promise<SelectProduct> {
  const result = await db.insert(products).values(productData).returning();
  return result[0];
}

/**
 * Update a product
 */
export async function updateProduct(id: string, productData: Partial<InsertProduct>): Promise<SelectProduct | null> {
  const result = await db
    .update(products)
    .set({ ...productData, updatedAt: new Date() })
    .where(eq(products.id, id))
    .returning();

  return result[0] || null;
}

/**
 * Delete a product (soft delete by setting status to inactive)
 */
export async function deleteProduct(id: string): Promise<boolean> {
  const result = await db
    .update(products)
    .set({ status: 'inactive', updatedAt: new Date() })
    .where(eq(products.id, id))
    .returning();

  return result.length > 0;
}

/**
 * Get available platforms
 */
export async function getAvailablePlatforms(): Promise<string[]> {
  // Use demo data if database is not configured
  if (!isFullyConfigured.database) {
    return demoData.platforms;
  }

  try {
    const result = await db
      .selectDistinct({ platform: products.platform })
      .from(products)
      .where(eq(products.status, 'active'));

    return result.map(r => r.platform);
  } catch (error) {
    console.error('Database query failed, falling back to demo data:', error);
    return demoData.platforms;
  }
}

/**
 * Get available regions for a platform
 */
export async function getAvailableRegions(platform?: string): Promise<string[]> {
  // Use demo data if database is not configured
  if (!isFullyConfigured.database) {
    return demoData.regions;
  }

  try {
    const conditions = [eq(products.status, 'active')];

    if (platform) {
      conditions.push(eq(products.platform, platform as 'switch' | 'ps' | 'xbox' | 'steam' | 'gift_card'));
    }

    const query = db
      .selectDistinct({ region: products.region })
      .from(products)
      .where(and(...conditions));

    const result = await query;
    return result.map(r => r.region);
  } catch (error) {
    console.error('Database query failed, falling back to demo data:', error);
    return demoData.regions;
  }
}

/**
 * Demo data functions for when database is not configured
 */
function getDemoProducts(
  filters: ProductFilters = {},
  options: ProductListOptions = {}
): ProductWithStock[] {
  let products = [...demoData.products];

  // Apply filters
  if (filters.platform) {
    products = products.filter(p => p.platform === filters.platform);
  }

  if (filters.region) {
    products = products.filter(p => p.region === filters.region);
  }

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    products = products.filter(p =>
      p.name.toLowerCase().includes(searchLower) ||
      p.description.toLowerCase().includes(searchLower)
    );
  }

  // Apply pagination
  const { limit = 50, offset = 0 } = options;
  products = products.slice(offset, offset + limit);

  return products;
}

export async function getDemoAvailablePlatforms(): Promise<string[]> {
  return demoData.platforms;
}

export async function getDemoAvailableRegions(): Promise<string[]> {
  return demoData.regions;
}

/**
 * Admin product filters interface
 */
export interface AdminProductFilters {
  search?: string;
  status?: string;
  platform?: string;
  category?: string; // Keep for compatibility but won't be used in filtering
  page?: number;
  limit?: number;
}

/**
 * Admin product with additional fields
 */
export interface AdminProductWithStock extends ProductWithStock {
  totalSales?: number;
}

/**
 * Get products for admin with filtering and pagination
 */
export async function getAdminProducts(
  filters: AdminProductFilters = {}
): Promise<{ products: AdminProductWithStock[]; total: number }> {
  const {
    search,
    status,
    platform,
    category,
    page = 1,
    limit = 50,
  } = filters;

  const offset = (page - 1) * limit;

  try {
    // Build conditions
    const conditions = [];

    if (status) {
      if (status === 'active') {
        conditions.push(eq(products.isActive, true));
      } else if (status === 'inactive') {
        conditions.push(eq(products.isActive, false));
      }
    }

    if (platform) {
      conditions.push(eq(products.platform, platform));
    }

    // Note: Category filtering disabled as database doesn't have category field
    // if (category) {
    //   conditions.push(eq(products.category, category));
    // }

    if (search) {
      conditions.push(
        or(
          like(products.name, `%${search}%`),
          like(products.description, `%${search}%`)
        )
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const countQuery = db
      .select({ count: sql<number>`count(*)` })
      .from(products)
      .$dynamic();

    const countResult = whereClause
      ? await countQuery.where(whereClause)
      : await countQuery;

    const total = countResult[0]?.count || 0;

    // Get products with basic information and actual stock count
    let query = db
      .select({
        id: products.id,
        name: products.name,
        platform: products.platform,
        region: products.region,
        currency: products.currency,
        amountCents: products.amountCents,
        description: products.description,
        slug: products.slug,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
        // Add the actual isActive field from database
        isActive: products.isActive,
        // Add default values for missing fields
        category: sql<string>`CASE WHEN platform = 'gift_card' THEN 'gift_card' ELSE 'game' END`,
        status: sql<string>`CASE WHEN is_active = true THEN 'active' ELSE 'inactive' END`,
        imageUrl: sql<string>`null`,
        systemRequirements: sql<string>`null`,
        features: sql<string[]>`ARRAY[]::text[]`,
        tags: sql<string[]>`ARRAY[]::text[]`,
        stockCount: sql<number>`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
        totalSales: sql<number>`0`, // Simplified: set total sales to 0 for now
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .groupBy(products.id, products.name, products.platform, products.region, products.currency, products.amountCents, products.description, products.slug, products.createdAt, products.updatedAt, products.isActive)
      .$dynamic();

    if (whereClause) {
      query = query.where(whereClause);
    }

    const result = await query
      .orderBy(desc(products.updatedAt))
      .limit(limit)
      .offset(offset);

    return {
      products: result,
      total,
    };
  } catch (error) {
    console.error('Error getting admin products:', error);
    return {
      products: [],
      total: 0,
    };
  }
}
