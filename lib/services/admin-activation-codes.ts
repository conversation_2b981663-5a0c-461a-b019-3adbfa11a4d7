import { db } from '@/lib/db';
import { activationCodes, products } from '@/lib/db/schema';
import { eq, and, or, like, sql, desc } from 'drizzle-orm';

export interface ActivationCodesStats {
  totalCodes: number;
  unusedCodes: number;
  usedCodes: number;
  lockedCodes: number;
  invalidCodes: number;
  lowStockProducts: number;
}

export interface AdminActivationCodeFilters {
  page?: number;
  limit?: number;
  product?: string;
  status?: string;
  search?: string;
}

export interface AdminActivationCode {
  id: string;
  productId: string;
  productName: string;
  productPlatform: string;
  code: string;
  status: string;
  lockedByOrderId: string | null;
  expiresAt: Date | null;
  deliveredAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Get activation codes statistics
 */
export async function getActivationCodesStats(): Promise<ActivationCodesStats> {
  try {
    // Get total counts by status
    const statusCounts = await db
      .select({
        status: activationCodes.status,
        count: sql<number>`count(*)`,
      })
      .from(activationCodes)
      .groupBy(activationCodes.status);

    const stats = {
      totalCodes: 0,
      unusedCodes: 0,
      usedCodes: 0,
      lockedCodes: 0,
      invalidCodes: 0,
      lowStockProducts: 0,
    };

    statusCounts.forEach(({ status, count }) => {
      stats.totalCodes += count;
      switch (status) {
        case 'unused':
          stats.unusedCodes = count;
          break;
        case 'used':
          stats.usedCodes = count;
          break;
        case 'locked':
          stats.lockedCodes = count;
          break;
        case 'invalid':
          stats.invalidCodes = count;
          break;
      }
    });

    // Get low stock products (less than or equal to 10 unused codes, only active products)
    const lowStockResult = await db
      .select({
        productId: products.id,
        unusedCount: sql<number>`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .where(eq(products.isActive, true))
      .groupBy(products.id)
      .having(sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0) <= 10`);

    stats.lowStockProducts = lowStockResult.length;

    return stats;
  } catch (error) {
    console.error('Error getting activation codes stats:', error);
    return {
      totalCodes: 0,
      unusedCodes: 0,
      usedCodes: 0,
      lockedCodes: 0,
      invalidCodes: 0,
      lowStockProducts: 0,
    };
  }
}

/**
 * Get activation codes for admin with filters and pagination
 */
export async function getAdminActivationCodes(
  filters: AdminActivationCodeFilters = {}
): Promise<{ codes: AdminActivationCode[]; pagination: PaginationInfo }> {
  const {
    page = 1,
    limit = 20,
    product,
    status,
    search,
  } = filters;

  const offset = (page - 1) * limit;

  try {
    // Build where conditions
    const conditions = [];

    if (product) {
      conditions.push(eq(activationCodes.productId, product));
    }

    if (status) {
      conditions.push(eq(activationCodes.status, status as any));
    }

    if (search) {
      conditions.push(
        or(
          like(activationCodes.code, `%${search}%`),
          like(products.name, `%${search}%`)
        )
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(activationCodes)
      .innerJoin(products, eq(activationCodes.productId, products.id))
      .where(whereClause);

    const total = countResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get activation codes
    const codesResult = await db
      .select({
        id: activationCodes.id,
        productId: activationCodes.productId,
        productName: products.name,
        productPlatform: products.platform,
        code: activationCodes.code,
        status: activationCodes.status,
        lockedByOrderId: activationCodes.lockedByOrderId,
        expiresAt: activationCodes.expiresAt,
        deliveredAt: activationCodes.deliveredAt,
        createdAt: activationCodes.createdAt,
        updatedAt: activationCodes.updatedAt,
      })
      .from(activationCodes)
      .innerJoin(products, eq(activationCodes.productId, products.id))
      .where(whereClause)
      .orderBy(desc(activationCodes.createdAt))
      .limit(limit)
      .offset(offset);

    const pagination: PaginationInfo = {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      codes: codesResult,
      pagination,
    };
  } catch (error) {
    console.error('Error getting admin activation codes:', error);
    return {
      codes: [],
      pagination: {
        page: 1,
        limit,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }
}

/**
 * Get products for filter dropdown
 */
export async function getProductsForFilter(): Promise<Array<{ id: string; name: string }>> {
  try {
    const result = await db
      .select({
        id: products.id,
        name: products.name,
      })
      .from(products)
      .orderBy(products.name);

    return result;
  } catch (error) {
    console.error('Error getting products for filter:', error);
    return [];
  }
}
