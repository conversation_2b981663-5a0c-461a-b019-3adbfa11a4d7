import { db } from '@/lib/db';
import { orders, users, afterSales, activationCodes, products } from '@/lib/db/schema';
import { eq, gte, lt, and, sql, desc, like, or, asc } from 'drizzle-orm';

export interface DashboardStats {
  todayOrders: number;
  todayOrdersChange: number;
  todayRevenue: number;
  todayRevenueChange: number;
  activeUsers: number;
  activeUsersChange: number;
  pendingAfterSales: number;
  pendingAfterSalesChange: number;
}

export interface RecentOrder {
  id: string;
  userEmail: string;
  productName: string;
  amountCents: number;
  status: string;
  createdAt: Date;
}

export interface RecentAfterSale {
  id: string;
  orderId: string;
  issueType: string;
  status: string;
  createdAt: Date;
}

export interface StockAlert {
  productId: string;
  productName: string;
  platform: string;
  currentStock: number;
  threshold: number;
}

export interface AdminOrderFilters {
  search?: string;
  status?: string;
  risk?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
}

export interface AdminOrderWithDetails {
  id: string;
  userId: string;
  userEmail: string;
  productId: string;
  productName: string;
  platform: string;
  currency: string;
  amountCents: number;
  status: string;
  riskLevel: string;
  paidAt: Date | null;
  deliveredAt: Date | null;
  refundedAt: Date | null;
  paymentIntentId: string;
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get dashboard statistics
 */
export async function getDashboardStats(): Promise<DashboardStats> {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const twoDaysAgo = new Date(yesterday.getTime() - 24 * 60 * 60 * 1000);

  try {
    // Today's orders
    const todayOrdersResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(orders)
      .where(gte(orders.createdAt, today));

    // Yesterday's orders
    const yesterdayOrdersResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(orders)
      .where(and(gte(orders.createdAt, yesterday), lt(orders.createdAt, today)));

    // Today's revenue
    const todayRevenueResult = await db
      .select({ sum: sql<number>`COALESCE(sum(amount_cents), 0)` })
      .from(orders)
      .where(and(
        gte(orders.createdAt, today),
        eq(orders.status, 'delivered')
      ));

    // Yesterday's revenue
    const yesterdayRevenueResult = await db
      .select({ sum: sql<number>`COALESCE(sum(amount_cents), 0)` })
      .from(orders)
      .where(and(
        gte(orders.createdAt, yesterday),
        lt(orders.createdAt, today),
        eq(orders.status, 'delivered')
      ));

    // Active users (logged in within last 30 days)
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const activeUsersResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(and(
        eq(users.isActive, true),
        gte(users.lastLoginAt, thirtyDaysAgo)
      ));

    // Pending after sales
    const pendingAfterSalesResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(afterSales)
      .where(eq(afterSales.status, 'pending'));

    // Yesterday's pending after sales
    const yesterdayPendingAfterSalesResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(afterSales)
      .where(and(
        eq(afterSales.status, 'pending'),
        gte(afterSales.createdAt, yesterday),
        lt(afterSales.createdAt, today)
      ));

    const todayOrders = todayOrdersResult[0]?.count || 0;
    const yesterdayOrders = yesterdayOrdersResult[0]?.count || 0;
    const todayRevenue = todayRevenueResult[0]?.sum || 0;
    const yesterdayRevenue = yesterdayRevenueResult[0]?.sum || 0;
    const activeUsers = activeUsersResult[0]?.count || 0;
    const pendingAfterSales = pendingAfterSalesResult[0]?.count || 0;
    const yesterdayPendingAfterSales = yesterdayPendingAfterSalesResult[0]?.count || 0;

    return {
      todayOrders,
      todayOrdersChange: yesterdayOrders > 0 ? Math.round(((todayOrders - yesterdayOrders) / yesterdayOrders) * 100) : 0,
      todayRevenue,
      todayRevenueChange: yesterdayRevenue > 0 ? Math.round(((todayRevenue - yesterdayRevenue) / yesterdayRevenue) * 100) : 0,
      activeUsers,
      activeUsersChange: 0, // Would need historical data to calculate
      pendingAfterSales,
      pendingAfterSalesChange: yesterdayPendingAfterSales > 0 ? Math.round(((pendingAfterSales - yesterdayPendingAfterSales) / yesterdayPendingAfterSales) * 100) : 0,
    };
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    return {
      todayOrders: 0,
      todayOrdersChange: 0,
      todayRevenue: 0,
      todayRevenueChange: 0,
      activeUsers: 0,
      activeUsersChange: 0,
      pendingAfterSales: 0,
      pendingAfterSalesChange: 0,
    };
  }
}

/**
 * Get recent orders for dashboard
 */
export async function getRecentOrders(limit: number = 10): Promise<RecentOrder[]> {
  try {
    const result = await db
      .select({
        id: orders.id,
        userEmail: users.email,
        productName: products.name,
        amountCents: orders.amountCents,
        status: orders.status,
        createdAt: orders.createdAt,
      })
      .from(orders)
      .innerJoin(users, eq(orders.userId, users.id))
      .innerJoin(products, eq(orders.productId, products.id))
      .orderBy(desc(orders.createdAt))
      .limit(limit);

    return result;
  } catch (error) {
    console.error('Error getting recent orders:', error);
    return [];
  }
}

/**
 * Get recent after sales for dashboard
 */
export async function getRecentAfterSales(limit: number = 10): Promise<RecentAfterSale[]> {
  try {
    const result = await db
      .select({
        id: afterSales.id,
        orderId: afterSales.orderId,
        issueType: afterSales.issueType,
        status: afterSales.status,
        createdAt: afterSales.createdAt,
      })
      .from(afterSales)
      .orderBy(desc(afterSales.createdAt))
      .limit(limit);

    return result;
  } catch (error) {
    console.error('Error getting recent after sales:', error);
    return [];
  }
}

/**
 * Get stock alerts for products with low inventory
 */
export async function getStockAlerts(threshold: number = 10): Promise<StockAlert[]> {
  try {
    const result = await db
      .select({
        productId: products.id,
        productName: products.name,
        platform: products.platform,
        currentStock: sql<number>`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .where(eq(products.isActive, true))
      .groupBy(products.id, products.name, products.platform)
      .having(sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0) <= ${threshold}`);

    return result.map(item => ({
      ...item,
      threshold,
    }));
  } catch (error) {
    console.error('Error getting stock alerts:', error);
    return [];
  }
}
