import { db } from '@/lib/db';
import { orders, users, products, orderDeliveries, activationCodes } from '@/lib/db/schema';
import { eq, and, or, like, gte, lte, desc, sql } from 'drizzle-orm';

export interface AdminOrderFilters {
  page?: number;
  limit?: number;
  status?: string;
  risk?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface AdminOrder {
  id: string;
  userId: string;
  userEmail: string;
  productId: string;
  productName: string;
  productPlatform: string;
  currency: string;
  amountCents: number;
  status: string;
  riskLevel: string;
  paidAt: Date | null;
  deliveredAt: Date | null;
  refundedAt: Date | null;
  paymentIntentId: string;
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface AdminOrderDetail extends AdminOrder {
  activationCode?: {
    id: string;
    code: string;
    status: string;
    deliveredAt: Date | null;
  };
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Get orders for admin with filters and pagination
 */
export async function getAdminOrders(
  filters: AdminOrderFilters = {}
): Promise<{ orders: AdminOrder[]; pagination: PaginationInfo }> {
  const {
    page = 1,
    limit = 20,
    status,
    risk,
    search,
    dateFrom,
    dateTo,
  } = filters;

  const offset = (page - 1) * limit;

  try {
    // Build where conditions
    const conditions = [];

    if (status) {
      conditions.push(eq(orders.status, status as any));
    }

    if (risk) {
      conditions.push(eq(orders.riskLevel, risk as any));
    }

    if (search) {
      conditions.push(
        or(
          like(orders.id, `%${search}%`),
          like(users.email, `%${search}%`),
          like(products.name, `%${search}%`)
        )
      );
    }

    if (dateFrom) {
      conditions.push(gte(orders.createdAt, new Date(dateFrom)));
    }

    if (dateTo) {
      const endDate = new Date(dateTo);
      endDate.setHours(23, 59, 59, 999);
      conditions.push(lte(orders.createdAt, endDate));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(orders)
      .innerJoin(users, eq(orders.userId, users.id))
      .innerJoin(products, eq(orders.productId, products.id))
      .where(whereClause);

    const total = countResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get orders
    const ordersResult = await db
      .select({
        id: orders.id,
        userId: orders.userId,
        userEmail: users.email,
        productId: orders.productId,
        productName: products.name,
        productPlatform: products.platform,
        currency: orders.currency,
        amountCents: orders.amountCents,
        status: orders.status,
        riskLevel: orders.riskLevel,
        paidAt: orders.paidAt,
        deliveredAt: orders.deliveredAt,
        refundedAt: orders.refundedAt,
        paymentIntentId: orders.paymentIntentId,
        metadata: orders.metadata,
        createdAt: orders.createdAt,
        updatedAt: orders.updatedAt,
      })
      .from(orders)
      .innerJoin(users, eq(orders.userId, users.id))
      .innerJoin(products, eq(orders.productId, products.id))
      .where(whereClause)
      .orderBy(desc(orders.createdAt))
      .limit(limit)
      .offset(offset);

    const pagination: PaginationInfo = {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      orders: ordersResult,
      pagination,
    };
  } catch (error) {
    console.error('Error getting admin orders:', error);
    return {
      orders: [],
      pagination: {
        page: 1,
        limit,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }
}

/**
 * Get order detail for admin
 */
export async function getAdminOrderDetail(orderId: string): Promise<AdminOrderDetail | null> {
  try {
    const result = await db
      .select({
        // Order fields
        id: orders.id,
        userId: orders.userId,
        userEmail: users.email,
        productId: orders.productId,
        productName: products.name,
        productPlatform: products.platform,
        currency: orders.currency,
        amountCents: orders.amountCents,
        status: orders.status,
        riskLevel: orders.riskLevel,
        paidAt: orders.paidAt,
        deliveredAt: orders.deliveredAt,
        refundedAt: orders.refundedAt,
        paymentIntentId: orders.paymentIntentId,
        metadata: orders.metadata,
        createdAt: orders.createdAt,
        updatedAt: orders.updatedAt,
        // Activation code fields
        activationCodeId: activationCodes.id,
        activationCode: activationCodes.code,
        activationCodeStatus: activationCodes.status,
        activationCodeDeliveredAt: orderDeliveries.deliveredAt,
      })
      .from(orders)
      .innerJoin(users, eq(orders.userId, users.id))
      .innerJoin(products, eq(orders.productId, products.id))
      .leftJoin(orderDeliveries, eq(orders.id, orderDeliveries.orderId))
      .leftJoin(activationCodes, eq(orderDeliveries.activationCodeId, activationCodes.id))
      .where(eq(orders.id, orderId))
      .limit(1);

    if (!result.length) {
      return null;
    }

    const order = result[0];
    
    return {
      id: order.id,
      userId: order.userId,
      userEmail: order.userEmail,
      productId: order.productId,
      productName: order.productName,
      productPlatform: order.productPlatform,
      currency: order.currency,
      amountCents: order.amountCents,
      status: order.status,
      riskLevel: order.riskLevel,
      paidAt: order.paidAt,
      deliveredAt: order.deliveredAt,
      refundedAt: order.refundedAt,
      paymentIntentId: order.paymentIntentId,
      metadata: order.metadata,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      activationCode: order.activationCodeId ? {
        id: order.activationCodeId,
        code: order.activationCode!,
        status: order.activationCodeStatus!,
        deliveredAt: order.activationCodeDeliveredAt,
      } : undefined,
    };
  } catch (error) {
    console.error('Error getting admin order detail:', error);
    return null;
  }
}
