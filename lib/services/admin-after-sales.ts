import { db } from '@/lib/db';
import { afterSales, orders, users, products } from '@/lib/db/schema';
import { eq, and, or, like, gte, lte, desc, sql } from 'drizzle-orm';

export interface AfterSalesStats {
  totalTickets: number;
  pendingTickets: number;
  resolvedTickets: number;
  rejectedTickets: number;
  avgResponseTime?: number;
}

export interface AdminAfterSalesFilters {
  page?: number;
  limit?: number;
  status?: string;
  issueType?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface AdminAfterSale {
  id: string;
  orderId: string;
  orderUserEmail: string;
  orderProductName: string;
  orderAmountCents: number;
  issueType: string;
  description: string | null;
  status: string;
  resolution: string | null;
  evidenceUrls: string[] | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface AdminAfterSaleDetail extends AdminAfterSale {
  order: {
    id: string;
    userId: string;
    userEmail: string;
    productId: string;
    productName: string;
    productPlatform: string;
    currency: string;
    amountCents: number;
    status: string;
    paymentIntentId: string;
    createdAt: Date;
  };
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Get after sales statistics
 */
export async function getAfterSalesStats(): Promise<AfterSalesStats> {
  try {
    // Get total counts by status
    const statusCounts = await db
      .select({
        status: afterSales.status,
        count: sql<number>`count(*)`,
      })
      .from(afterSales)
      .groupBy(afterSales.status);

    const stats = {
      totalTickets: 0,
      pendingTickets: 0,
      resolvedTickets: 0,
      rejectedTickets: 0,
    };

    statusCounts.forEach(({ status, count }) => {
      stats.totalTickets += count;
      switch (status) {
        case 'pending':
          stats.pendingTickets = count;
          break;
        case 'resolved':
          stats.resolvedTickets = count;
          break;
        case 'rejected':
          stats.rejectedTickets = count;
          break;
      }
    });

    // Calculate average response time for resolved tickets (simplified)
    const avgResponseResult = await db
      .select({
        avgHours: sql<number>`AVG(EXTRACT(EPOCH FROM (updated_at - created_at)) / 3600)`,
      })
      .from(afterSales)
      .where(eq(afterSales.status, 'resolved'));

    const avgResponseTime = avgResponseResult[0]?.avgHours 
      ? Math.round(avgResponseResult[0].avgHours)
      : undefined;

    return {
      ...stats,
      avgResponseTime,
    };
  } catch (error) {
    console.error('Error getting after sales stats:', error);
    return {
      totalTickets: 0,
      pendingTickets: 0,
      resolvedTickets: 0,
      rejectedTickets: 0,
    };
  }
}

/**
 * Get after sales tickets for admin with filters and pagination
 */
export async function getAdminAfterSales(
  filters: AdminAfterSalesFilters = {}
): Promise<{ tickets: AdminAfterSale[]; pagination: PaginationInfo }> {
  const {
    page = 1,
    limit = 20,
    status,
    issueType,
    search,
    dateFrom,
    dateTo,
  } = filters;

  const offset = (page - 1) * limit;

  try {
    // Build where conditions
    const conditions = [];

    if (status) {
      conditions.push(eq(afterSales.status, status as any));
    }

    if (issueType) {
      conditions.push(eq(afterSales.issueType, issueType));
    }

    if (search) {
      conditions.push(
        or(
          like(afterSales.id, `%${search}%`),
          like(afterSales.description, `%${search}%`),
          like(orders.id, `%${search}%`),
          like(users.email, `%${search}%`),
          like(products.name, `%${search}%`)
        )
      );
    }

    if (dateFrom) {
      conditions.push(gte(afterSales.createdAt, new Date(dateFrom)));
    }

    if (dateTo) {
      const endDate = new Date(dateTo);
      endDate.setHours(23, 59, 59, 999);
      conditions.push(lte(afterSales.createdAt, endDate));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(afterSales)
      .innerJoin(orders, eq(afterSales.orderId, orders.id))
      .innerJoin(users, eq(orders.userId, users.id))
      .innerJoin(products, eq(orders.productId, products.id))
      .where(whereClause);

    const total = countResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get after sales tickets
    const ticketsResult = await db
      .select({
        id: afterSales.id,
        orderId: afterSales.orderId,
        orderUserEmail: users.email,
        orderProductName: products.name,
        orderAmountCents: orders.amountCents,
        issueType: afterSales.issueType,
        description: afterSales.description,
        status: afterSales.status,
        resolution: afterSales.resolution,
        evidenceUrls: afterSales.evidenceUrls,
        createdAt: afterSales.createdAt,
        updatedAt: afterSales.updatedAt,
      })
      .from(afterSales)
      .innerJoin(orders, eq(afterSales.orderId, orders.id))
      .innerJoin(users, eq(orders.userId, users.id))
      .innerJoin(products, eq(orders.productId, products.id))
      .where(whereClause)
      .orderBy(desc(afterSales.createdAt))
      .limit(limit)
      .offset(offset);

    const pagination: PaginationInfo = {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      tickets: ticketsResult,
      pagination,
    };
  } catch (error) {
    console.error('Error getting admin after sales:', error);
    return {
      tickets: [],
      pagination: {
        page: 1,
        limit,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }
}

/**
 * Get after sales ticket detail for admin
 */
export async function getAdminAfterSaleDetail(ticketId: string): Promise<AdminAfterSaleDetail | null> {
  try {
    const result = await db
      .select({
        // After sales fields
        id: afterSales.id,
        orderId: afterSales.orderId,
        issueType: afterSales.issueType,
        description: afterSales.description,
        status: afterSales.status,
        resolution: afterSales.resolution,
        evidenceUrls: afterSales.evidenceUrls,
        createdAt: afterSales.createdAt,
        updatedAt: afterSales.updatedAt,
        // Order fields
        orderUserId: orders.userId,
        orderUserEmail: users.email,
        orderProductId: orders.productId,
        orderProductName: products.name,
        orderProductPlatform: products.platform,
        orderCurrency: orders.currency,
        orderAmountCents: orders.amountCents,
        orderStatus: orders.status,
        orderPaymentIntentId: orders.paymentIntentId,
        orderCreatedAt: orders.createdAt,
      })
      .from(afterSales)
      .innerJoin(orders, eq(afterSales.orderId, orders.id))
      .innerJoin(users, eq(orders.userId, users.id))
      .innerJoin(products, eq(orders.productId, products.id))
      .where(eq(afterSales.id, ticketId))
      .limit(1);

    if (!result.length) {
      return null;
    }

    const ticket = result[0];
    
    return {
      id: ticket.id,
      orderId: ticket.orderId,
      orderUserEmail: ticket.orderUserEmail,
      orderProductName: ticket.orderProductName,
      orderAmountCents: ticket.orderAmountCents,
      issueType: ticket.issueType,
      description: ticket.description,
      status: ticket.status,
      resolution: ticket.resolution,
      evidenceUrls: ticket.evidenceUrls,
      createdAt: ticket.createdAt,
      updatedAt: ticket.updatedAt,
      order: {
        id: ticket.orderId,
        userId: ticket.orderUserId,
        userEmail: ticket.orderUserEmail,
        productId: ticket.orderProductId,
        productName: ticket.orderProductName,
        productPlatform: ticket.orderProductPlatform,
        currency: ticket.orderCurrency,
        amountCents: ticket.orderAmountCents,
        status: ticket.orderStatus,
        paymentIntentId: ticket.orderPaymentIntentId,
        createdAt: ticket.orderCreatedAt,
      },
    };
  } catch (error) {
    console.error('Error getting admin after sale detail:', error);
    return null;
  }
}
