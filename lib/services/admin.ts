import { createClient } from '@/lib/supabase/server';
import type { SelectUser } from '@/lib/db/schema';

export type AdminUser = SelectUser & {
  isAdmin: boolean;
  isSuperAdmin: boolean;
};

/**
 * Check if a user has admin privileges
 */
export async function checkAdminAccess(userId: string): Promise<AdminUser | null> {
  console.log('=== checkAdminAccess: Starting ===', { userId });

  try {
    console.log('=== checkAdminAccess: Querying database for user via Supabase ===');

    const supabase = await createClient();
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log('=== checkAdminAccess: Supabase query result ===', { user, error });

    if (error || !user || !user.is_active) {
      console.log('=== checkAdminAccess: User not found or inactive ===', { error, user });
      return null;
    }

    const isAdmin = user.role === 'admin' || user.role === 'super_admin';
    const isSuperAdmin = user.role === 'super_admin';

    console.log('=== checkAdminAccess: Role check ===', { role: user.role, isAdmin, isSuperAdmin });

    if (!isAdmin) {
      console.log('=== checkAdminAccess: User is not admin ===');
      return null;
    }

    console.log('=== checkAdminAccess: Success ===');

    // Convert Supabase response to match our type
    const adminUser: AdminUser = {
      id: user.id,
      email: user.email,
      role: user.role as 'user' | 'admin' | 'super_admin',
      isActive: user.is_active,
      lastLoginAt: user.last_login_at ? new Date(user.last_login_at) : null,
      createdAt: new Date(user.created_at),
      updatedAt: new Date(user.updated_at),
      isAdmin,
      isSuperAdmin,
    };

    return adminUser;
  } catch (error) {
    console.error('=== checkAdminAccess: Error ===', error);
    return null;
  }
}

/**
 * Get current admin user from session
 */
export async function getCurrentAdminUser(): Promise<AdminUser | null> {
  console.log('=== getCurrentAdminUser: Starting ===');
  
  try {
    const supabase = await createClient();
    console.log('=== getCurrentAdminUser: Supabase client created ===');
    
    const { data: { user }, error } = await supabase.auth.getUser();
    
    console.log('=== getCurrentAdminUser: Supabase auth result ===', {
      hasUser: !!user,
      userId: user?.id,
      email: user?.email,
      error: error?.message
    });

    if (error || !user) {
      console.log('=== getCurrentAdminUser: No authenticated user found ===');
      return null;
    }

    console.log('=== getCurrentAdminUser: Calling checkAdminAccess ===');
    const adminUser = await checkAdminAccess(user.id);
    
    console.log('=== getCurrentAdminUser: checkAdminAccess result ===', {
      hasAdminUser: !!adminUser,
      adminUserId: adminUser?.id,
      adminEmail: adminUser?.email,
      role: adminUser?.role,
      isAdmin: adminUser?.isAdmin,
      isSuperAdmin: adminUser?.isSuperAdmin
    });
    
    return adminUser;
  } catch (error) {
    console.error('=== getCurrentAdminUser: Error ===', error);
    return null;
  }
}

/**
 * Require admin access - throws error if not admin
 */
export async function requireAdminAccess(): Promise<AdminUser> {
  const adminUser = await getCurrentAdminUser();
  
  if (!adminUser) {
    throw new Error('Admin access required');
  }

  return adminUser;
}

/**
 * Require super admin access - throws error if not super admin
 */
export async function requireSuperAdminAccess(): Promise<AdminUser> {
  const adminUser = await requireAdminAccess();
  
  if (!adminUser.isSuperAdmin) {
    throw new Error('Super admin access required');
  }

  return adminUser;
}

/**
 * Log admin action for audit trail
 */
export async function logAdminAction(
  adminId: string,
  action: string,
  details: Record<string, any> = {}
): Promise<void> {
  try {
    await db.insert(auditLogs).values({
      actorId: adminId,
      actorType: 'admin',
      action,
      details: {
        ...details,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error logging admin action:', error);
  }
}

/**
 * Update user role (super admin only)
 */
export async function updateUserRole(
  targetUserId: string,
  newRole: 'user' | 'admin' | 'super_admin',
  adminId: string
): Promise<SelectUser | null> {
  // Verify super admin access
  await requireSuperAdminAccess();

  try {
    const result = await db
      .update(users)
      .set({
        role: newRole,
        updatedAt: new Date(),
      })
      .where(eq(users.id, targetUserId))
      .returning();

    if (result.length > 0) {
      // Log the role change
      await logAdminAction(adminId, 'user_role_updated', {
        targetUserId,
        newRole,
        previousRole: 'unknown', // We could fetch this first if needed
      });
    }

    return result[0] || null;
  } catch (error) {
    console.error('Error updating user role:', error);
    throw error;
  }
}

/**
 * Deactivate user account (admin only)
 */
export async function deactivateUser(
  targetUserId: string,
  adminId: string,
  reason?: string
): Promise<SelectUser | null> {
  // Verify admin access
  await requireAdminAccess();

  try {
    const result = await db
      .update(users)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(users.id, targetUserId))
      .returning();

    if (result.length > 0) {
      // Log the deactivation
      await logAdminAction(adminId, 'user_deactivated', {
        targetUserId,
        reason: reason || 'No reason provided',
      });
    }

    return result[0] || null;
  } catch (error) {
    console.error('Error deactivating user:', error);
    throw error;
  }
}

/**
 * Reactivate user account (admin only)
 */
export async function reactivateUser(
  targetUserId: string,
  adminId: string
): Promise<SelectUser | null> {
  // Verify admin access
  await requireAdminAccess();

  try {
    const result = await db
      .update(users)
      .set({
        isActive: true,
        updatedAt: new Date(),
      })
      .where(eq(users.id, targetUserId))
      .returning();

    if (result.length > 0) {
      // Log the reactivation
      await logAdminAction(adminId, 'user_reactivated', {
        targetUserId,
      });
    }

    return result[0] || null;
  } catch (error) {
    console.error('Error reactivating user:', error);
    throw error;
  }
}
