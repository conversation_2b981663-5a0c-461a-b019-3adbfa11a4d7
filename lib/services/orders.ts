import { db } from '@/lib/db';
import { orders, products, users, activationCodes, orderDeliveries, auditLogs } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';
import type { SelectOrder, SelectProduct, SelectActivationCode } from '@/lib/db/schema';

export interface OrderWithDetails extends SelectOrder {
  product: SelectProduct;
  user: { id: string; email: string };
  activationCode?: SelectActivationCode;
}

export interface CreateOrderData {
  userId: string;
  productId: string;
  paymentIntentId: string;
  currency: string;
  amountCents: number;
  metadata?: Record<string, unknown>;
}

/**
 * Create a new order
 */
export async function createOrder(orderData: CreateOrderData): Promise<SelectOrder> {
  const result = await db.insert(orders).values({
    userId: orderData.userId,
    productId: orderData.productId,
    paymentIntentId: orderData.paymentIntentId,
    currency: orderData.currency,
    amountCents: orderData.amountCents,
    status: 'pending',
    riskLevel: 'low', // Will be updated by risk assessment
    metadata: orderData.metadata || {},
  }).returning();

  // Log order creation
  await db.insert(auditLogs).values({
    actorId: orderData.userId,
    actorType: 'user',
    action: 'order_created',
    details: {
      orderId: result[0].id,
      productId: orderData.productId,
      amountCents: orderData.amountCents,
    },
  });

  return result[0];
}

/**
 * Get order by ID with full details
 */
export async function getOrderById(orderId: string): Promise<OrderWithDetails | null> {
  const result = await db
    .select({
      // Order fields
      id: orders.id,
      userId: orders.userId,
      productId: orders.productId,
      currency: orders.currency,
      amountCents: orders.amountCents,
      status: orders.status,
      paidAt: orders.paidAt,
      deliveredAt: orders.deliveredAt,
      refundedAt: orders.refundedAt,
      paymentIntentId: orders.paymentIntentId,
      riskLevel: orders.riskLevel,
      metadata: orders.metadata,
      createdAt: orders.createdAt,
      updatedAt: orders.updatedAt,
      // Product fields
      product: {
        id: products.id,
        name: products.name,
        platform: products.platform,
        region: products.region,
        currency: products.currency,
        amountCents: products.amountCents,
        description: products.description,
        status: products.status,
        slug: products.slug,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      },
      // User fields
      user: {
        id: users.id,
        email: users.email,
      },
      // Activation code (if delivered)
      activationCode: {
        id: activationCodes.id,
        productId: activationCodes.productId,
        code: activationCodes.code,
        status: activationCodes.status,
        lockedByOrderId: activationCodes.lockedByOrderId,
        expiresAt: activationCodes.expiresAt,
        deliveredAt: activationCodes.deliveredAt,
        createdAt: activationCodes.createdAt,
        updatedAt: activationCodes.updatedAt,
      },
    })
    .from(orders)
    .innerJoin(products, eq(orders.productId, products.id))
    .innerJoin(users, eq(orders.userId, users.id))
    .leftJoin(orderDeliveries, eq(orders.id, orderDeliveries.orderId))
    .leftJoin(activationCodes, eq(orderDeliveries.activationCodeId, activationCodes.id))
    .where(eq(orders.id, orderId))
    .limit(1);

  if (result.length === 0) {
    return null;
  }

  const row = result[0];
  return {
    ...row,
    product: row.product as SelectProduct,
    user: row.user,
    activationCode: row.activationCode?.id ? row.activationCode as SelectActivationCode : undefined,
  };
}

/**
 * Get orders by user ID
 */
export async function getOrdersByUserId(
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<OrderWithDetails[]> {
  const result = await db
    .select({
      // Order fields
      id: orders.id,
      userId: orders.userId,
      productId: orders.productId,
      currency: orders.currency,
      amountCents: orders.amountCents,
      status: orders.status,
      paidAt: orders.paidAt,
      deliveredAt: orders.deliveredAt,
      refundedAt: orders.refundedAt,
      paymentIntentId: orders.paymentIntentId,
      riskLevel: orders.riskLevel,
      metadata: orders.metadata,
      createdAt: orders.createdAt,
      updatedAt: orders.updatedAt,
      // Product fields
      product: {
        id: products.id,
        name: products.name,
        platform: products.platform,
        region: products.region,
        currency: products.currency,
        amountCents: products.amountCents,
        description: products.description,
        status: products.status,
        slug: products.slug,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      },
      // User fields
      user: {
        id: users.id,
        email: users.email,
      },
      // Activation code (if delivered)
      activationCode: {
        id: activationCodes.id,
        productId: activationCodes.productId,
        code: activationCodes.code,
        status: activationCodes.status,
        lockedByOrderId: activationCodes.lockedByOrderId,
        expiresAt: activationCodes.expiresAt,
        deliveredAt: activationCodes.deliveredAt,
        createdAt: activationCodes.createdAt,
        updatedAt: activationCodes.updatedAt,
      },
    })
    .from(orders)
    .innerJoin(products, eq(orders.productId, products.id))
    .innerJoin(users, eq(orders.userId, users.id))
    .leftJoin(orderDeliveries, eq(orders.id, orderDeliveries.orderId))
    .leftJoin(activationCodes, eq(orderDeliveries.activationCodeId, activationCodes.id))
    .where(eq(orders.userId, userId))
    .orderBy(desc(orders.createdAt))
    .limit(limit)
    .offset(offset);

  return result.map(row => ({
    ...row,
    product: row.product as SelectProduct,
    user: row.user,
    activationCode: row.activationCode?.id ? row.activationCode as SelectActivationCode : undefined,
  }));
}

/**
 * Update order status
 */
export async function updateOrderStatus(
  orderId: string,
  status: 'pending' | 'paid' | 'delivered' | 'refunded' | 'under_review',
  actorId?: string,
  actorType: 'user' | 'admin' | 'system' = 'system'
): Promise<SelectOrder | null> {
  const updateData: Partial<SelectOrder> = {
    status,
    updatedAt: new Date(),
  };

  // Set timestamp based on status
  if (status === 'paid') {
    updateData.paidAt = new Date();
  } else if (status === 'delivered') {
    updateData.deliveredAt = new Date();
  } else if (status === 'refunded') {
    updateData.refundedAt = new Date();
  }

  const result = await db
    .update(orders)
    .set(updateData)
    .where(eq(orders.id, orderId))
    .returning();

  if (result.length > 0) {
    // Log status change
    await db.insert(auditLogs).values({
      actorId,
      actorType,
      action: 'order_status_updated',
      details: {
        orderId,
        newStatus: status,
        timestamp: new Date().toISOString(),
      },
    });
  }

  return result[0] || null;
}

/**
 * Update order risk level
 */
export async function updateOrderRiskLevel(
  orderId: string,
  riskLevel: 'low' | 'medium' | 'high',
  actorId?: string
): Promise<SelectOrder | null> {
  const result = await db
    .update(orders)
    .set({
      riskLevel,
      updatedAt: new Date(),
    })
    .where(eq(orders.id, orderId))
    .returning();

  if (result.length > 0) {
    // Log risk level change
    await db.insert(auditLogs).values({
      actorId,
      actorType: 'system',
      action: 'order_risk_updated',
      details: {
        orderId,
        newRiskLevel: riskLevel,
        timestamp: new Date().toISOString(),
      },
    });
  }

  return result[0] || null;
}

/**
 * Get order by payment intent ID
 */
export async function getOrderByPaymentIntentId(paymentIntentId: string): Promise<SelectOrder | null> {
  const result = await db
    .select()
    .from(orders)
    .where(eq(orders.paymentIntentId, paymentIntentId))
    .limit(1);

  return result[0] || null;
}
