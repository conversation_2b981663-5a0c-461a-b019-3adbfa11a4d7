/**
 * Application Configuration
 * 
 * This file manages configuration and feature flags based on environment setup.
 */

// Check if we're in development mode
export const isDevelopment = process.env.NODE_ENV === 'development';

// Check if all required services are configured
export const isFullyConfigured = {
  database: process.env.DATABASE_URL && !process.env.DATABASE_URL.includes('[YOUR-PASSWORD]'),
  stripe: process.env.STRIPE_SECRET_KEY && !process.env.STRIPE_SECRET_KEY.includes('your-'),
  email: process.env.RESEND_API_KEY && !process.env.RESEND_API_KEY.includes('your-'),
};

// Overall configuration status
export const isProductionReady = Object.values(isFullyConfigured).every(Boolean);

// Feature flags based on configuration
export const features = {
  // Core features that work without external services
  productBrowsing: true,
  userAuthentication: isFullyConfigured.database,
  
  // Features that require external services
  payments: isFullyConfigured.stripe,
  emailNotifications: isFullyConfigured.email,
  orderProcessing: isFullyConfigured.database && isFullyConfigured.stripe,
  
  // Demo mode when not fully configured
  demoMode: !isProductionReady && isDevelopment,
};

// Configuration messages for users
export const configMessages = {
  database: !isFullyConfigured.database 
    ? 'Database not configured. Please set DATABASE_URL with your actual password.'
    : null,
  
  stripe: !isFullyConfigured.stripe 
    ? 'Stripe not configured. Payments will be disabled.'
    : null,
  
  email: !isFullyConfigured.email 
    ? 'Email service not configured. Email notifications will be disabled.'
    : null,
};

// Get active configuration warnings
export const getConfigWarnings = (): string[] => {
  return Object.values(configMessages).filter(Boolean) as string[];
};

// Demo data for when services aren't configured
export const demoData = {
  products: [
    {
      id: 'demo-1',
      name: 'The Legend of Zelda: Breath of the Wild',
      platform: 'switch' as const,
      region: 'US',
      currency: 'USD',
      amountCents: 5999,
      description: 'Demo product - payments disabled in demo mode',
      slug: 'zelda-demo',
      isActive: true,
      stockCount: 10,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'demo-2',
      name: 'Cyberpunk 2077',
      platform: 'steam' as const,
      region: 'Global',
      currency: 'USD',
      amountCents: 2999,
      description: 'Demo product - payments disabled in demo mode',
      slug: 'cyberpunk-demo',
      isActive: true,
      stockCount: 5,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
  
  platforms: ['switch', 'steam', 'ps', 'xbox', 'gift_card'],
  regions: ['US', 'EU', 'Global'],
};

// Environment setup instructions
export const setupInstructions = {
  database: {
    title: 'Database Setup Required',
    description: 'Configure your Supabase database connection',
    steps: [
      'Go to your Supabase project dashboard',
      'Navigate to Settings > Database',
      'Copy the connection string',
      'Replace [YOUR-PASSWORD] in DATABASE_URL with your actual password',
    ],
  },
  
  stripe: {
    title: 'Stripe Setup Required',
    description: 'Configure Stripe for payment processing',
    steps: [
      'Create a Stripe account at stripe.com',
      'Get your API keys from the Stripe dashboard',
      'Set STRIPE_SECRET_KEY and NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
      'Create a webhook endpoint for payment notifications',
    ],
  },
  
  email: {
    title: 'Email Service Setup Required',
    description: 'Configure Resend for email notifications',
    steps: [
      'Create a Resend account at resend.com',
      'Get your API key from the Resend dashboard',
      'Set RESEND_API_KEY and FROM_EMAIL',
    ],
  },
};

const config = {
  isDevelopment,
  isFullyConfigured,
  isProductionReady,
  features,
  configMessages,
  getConfigWarnings,
  demoData,
  setupInstructions,
};

export default config;
