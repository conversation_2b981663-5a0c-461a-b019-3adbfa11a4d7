import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// In a Next.js application, environment variables from `.env.local` are
// automatically loaded into `process.env` by Next at build/runtime. We rely on that
// behaviour here so that no additional configuration is necessary.

if (!process.env.DATABASE_URL) {
  throw new Error('Missing env var: DATABASE_URL');
}

// Create a Postgres.js client. A single connection is usually enough for
// server-less / edge environments, but you can adjust the pool size to your needs.
const queryClient = postgres(process.env.DATABASE_URL, {
  max: 1, // keep the pool as small as possible for lambda-style execution
});

// Wrap the client with Drizzle ORM to get a fully-typed query builder.
export const db = drizzle(queryClient);
