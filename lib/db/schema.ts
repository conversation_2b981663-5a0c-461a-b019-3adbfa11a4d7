/*
 * Database schema definition (Drizzle ORM)
 * --------------------------------------------------
 * This file defines all public tables used by the application, following
 * the Product Requirement Document and subsequent discussion.
 *
 * NOTE: Supabase automatically provisions an `auth.users` table for core
 * authentication data. The `users` table below is an *extension* table for
 * additional profile fields we control explicitly. If you prefer to avoid a
 * shadow table, switch to using the `auth.users` table directly and adjust
 * FKs accordingly.
 */

import {
  pgTable,
  pgEnum,
  uuid,
  text,
  timestamp,
  boolean,
  integer,
  jsonb,
  index,
  unique,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

/* -------------------------------------------------------------------------- */
/* ENUMS                                                                      */
/* -------------------------------------------------------------------------- */

export const platformEnum = pgEnum('platform', [
  'steam',
  'epic',
  'origin',
  'uplay',
  'battlenet',
  'gog',
  'switch',
  'ps',
  'xbox',
  'gift_card',
]);

export const productStatusEnum = pgEnum('product_status', [
  'draft',
  'active',
  'inactive',
]);

export const productCategoryEnum = pgEnum('product_category', [
  'action',
  'adventure',
  'rpg',
  'strategy',
  'simulation',
  'sports',
  'racing',
  'puzzle',
  'indie',
  'horror',
  'fighting',
  'shooter',
]);

export const activationCodeStatusEnum = pgEnum('activation_code_status', [
  'unused',
  'locked',
  'used',
  'invalid',
]);

export const orderStatusEnum = pgEnum('order_status', [
  'pending',
  'paid',
  'delivered',
  'refunded',
  'under_review',
]);

export const riskLevelEnum = pgEnum('risk_level', ['low', 'medium', 'high']);

export const afterSaleStatusEnum = pgEnum('after_sale_status', [
  'pending',
  'resolved',
  'rejected',
]);

export const actorTypeEnum = pgEnum('actor_type', ['user', 'admin', 'system']);

export const userRoleEnum = pgEnum('user_role', ['user', 'admin', 'super_admin']);

/* -------------------------------------------------------------------------- */
/* USERS                                                                      */
/* -------------------------------------------------------------------------- */

export const users = pgTable(
  'users',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    email: text('email').notNull().unique(),
    role: userRoleEnum('role').default('user').notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    lastLoginAt: timestamp('last_login_at', { withTimezone: true }),
    createdAt: timestamp('created_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date())
      .notNull(),
  },
  (table) => {
    return {
      emailIdx: index('users_email_idx').on(table.email),
      roleIdx: index('users_role_idx').on(table.role),
    };
  },
);

/* -------------------------------------------------------------------------- */
/* PRODUCTS                                                                   */
/* -------------------------------------------------------------------------- */

export const products = pgTable(
  'products',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    name: text('name').notNull(),
    platform: platformEnum('platform').notNull(),
    category: productCategoryEnum('category').notNull(),
    region: text('region').notNull(),

    // Pricing: currency (ISO-4217 3‐letter) + amount in minor units (cents)
    currency: text('currency').default('USD').notNull(),
    amountCents: integer('amount_cents').notNull(),

    description: text('description'),
    // Rich text content for product details
    detailContent: text('detail_content'),
    isActive: boolean('is_active').default(true).notNull(),
    slug: text('slug'),

    // Media fields
    mainImageUrl: text('main_image_url'),
    videoUrl: text('video_url'),
    imageUrls: text('image_urls').array().default(sql`ARRAY[]::text[]`),

    // Legacy field for backward compatibility
    imageUrl: text('image_url'),

    systemRequirements: text('system_requirements'),
    features: text('features').array(),
    tags: text('tags').array(),

    createdAt: timestamp('created_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date())
      .notNull(),
  },
  (table) => {
    return {
      nameIdx: index('products_name_idx').on(table.name),
      platformIdx: index('products_platform_idx').on(table.platform),
      categoryIdx: index('products_category_idx').on(table.category),
      isActiveIdx: index('products_is_active_idx').on(table.isActive),
      slugUnique: unique('products_slug_unique').on(table.slug),
    };
  },
);

/* -------------------------------------------------------------------------- */
/* ORDERS                                                                     */
/* -------------------------------------------------------------------------- */

export const orders = pgTable(
  'orders',
  {
    id: uuid('id').defaultRandom().primaryKey(),

    userId: uuid('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    productId: uuid('product_id')
      .references(() => products.id, { onDelete: 'restrict' })
      .notNull(),

    currency: text('currency').notNull(),
    amountCents: integer('amount_cents').notNull(),

    status: orderStatusEnum('status').default('pending').notNull(),

    paidAt: timestamp('paid_at', { withTimezone: true }),
    deliveredAt: timestamp('delivered_at', { withTimezone: true }),
    refundedAt: timestamp('refunded_at', { withTimezone: true }),

    paymentIntentId: text('payment_intent_id').unique().notNull(),
    riskLevel: riskLevelEnum('risk_level').default('low').notNull(),
    metadata: jsonb('metadata').default(sql`'{}'::jsonb`).notNull(),

    createdAt: timestamp('created_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date())
      .notNull(),
  },
  (table) => {
    return {
      userCreatedIdx: index('orders_user_created_idx').on(
        table.userId,
        table.createdAt,
      ),
      statusIdx: index('orders_status_idx').on(table.status),
    };
  },
);

/* -------------------------------------------------------------------------- */
/* ACTIVATION CODES                                                           */
/* -------------------------------------------------------------------------- */

export const activationCodes = pgTable(
  'activation_codes',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    productId: uuid('product_id')
      .references(() => products.id, { onDelete: 'cascade' })
      .notNull(),

    code: text('code').notNull(),
    status: activationCodeStatusEnum('status').default('unused').notNull(),
    lockedByOrderId: uuid('locked_by_order_id').references(() => orders.id),
    expiresAt: timestamp('expires_at', { withTimezone: true }),
    deliveredAt: timestamp('delivered_at', { withTimezone: true }),

    createdAt: timestamp('created_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date())
      .notNull(),
  },
  (table) => {
    return {
      productStatusIdx: index('activation_codes_product_status_idx').on(
        table.productId,
        table.status,
      ),
      codeUnique: unique('activation_codes_code_unique').on(table.code),
    };
  },
);

/* -------------------------------------------------------------------------- */
/* ORDER DELIVERIES                                                           */
/* -------------------------------------------------------------------------- */

export const orderDeliveries = pgTable(
  'order_deliveries',
  {
    id: uuid('id').defaultRandom().primaryKey(),

    orderId: uuid('order_id')
      .references(() => orders.id, { onDelete: 'cascade' })
      .notNull(),
    activationCodeId: uuid('activation_code_id')
      .references(() => activationCodes.id, { onDelete: 'restrict' })
      .notNull(),

    deliveredAt: timestamp('delivered_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      orderUnique: unique('order_deliveries_order_unique').on(table.orderId),
    };
  },
);

/* -------------------------------------------------------------------------- */
/* AFTER SALES                                                                */
/* -------------------------------------------------------------------------- */

export const afterSales = pgTable(
  'after_sales',
  {
    id: uuid('id').defaultRandom().primaryKey(),

    orderId: uuid('order_id')
      .references(() => orders.id, { onDelete: 'cascade' })
      .notNull(),

    issueType: text('issue_type').notNull(),
    description: text('description'),
    status: afterSaleStatusEnum('status').default('pending').notNull(),
    resolution: text('resolution'),

    evidenceUrls: text('evidence_urls').array(),

    createdAt: timestamp('created_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date())
      .notNull(),
  },
  (table) => {
    return {
      orderIdx: index('after_sales_order_idx').on(table.orderId),
    };
  },
);

/* -------------------------------------------------------------------------- */
/* AUDIT LOGS                                                                 */
/* -------------------------------------------------------------------------- */

export const auditLogs = pgTable('audit_logs', {
  id: uuid('id').defaultRandom().primaryKey(),
  actorId: uuid('actor_id'),
  actorType: actorTypeEnum('actor_type').notNull(),
  action: text('action').notNull(),
  details: jsonb('details').default(sql`'{}'::jsonb`).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true })
    .defaultNow()
    .notNull(),
});

/* -------------------------------------------------------------------------- */
/* TYPE EXPORTS                                                               */
/* -------------------------------------------------------------------------- */

export type InsertUser = typeof users.$inferInsert;
export type SelectUser = typeof users.$inferSelect;

export type InsertProduct = typeof products.$inferInsert;
export type SelectProduct = typeof products.$inferSelect;

export type InsertActivationCode = typeof activationCodes.$inferInsert;
export type SelectActivationCode = typeof activationCodes.$inferSelect;

export type InsertOrder = typeof orders.$inferInsert;
export type SelectOrder = typeof orders.$inferSelect;

export type InsertOrderDelivery = typeof orderDeliveries.$inferInsert;
export type SelectOrderDelivery = typeof orderDeliveries.$inferSelect;

export type InsertAfterSale = typeof afterSales.$inferInsert;
export type SelectAfterSale = typeof afterSales.$inferSelect;

export type InsertAuditLog = typeof auditLogs.$inferInsert;
export type SelectAuditLog = typeof auditLogs.$inferSelect;
