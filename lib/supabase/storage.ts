import { createClient } from './client';

export const STORAGE_BUCKETS = {
  GAMES_SOURCE: 'games.source',
} as const;

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
  path?: string;
}

/**
 * Upload a file to Supabase storage
 */
export async function uploadFile(
  file: File,
  bucket: string = STORAGE_BUCKETS.GAMES_SOURCE,
  folder?: string
): Promise<UploadResult> {
  try {
    const supabase = createClient();
    
    // Generate unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
    const filePath = folder ? `${folder}/${fileName}` : fileName;

    // Upload file
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Upload error:', error);
      let errorMessage = error.message;

      // Handle specific error types
      if (error.message.includes('row-level security policy')) {
        errorMessage = '文件上传权限不足，请联系管理员配置存储权限';
      } else if (error.message.includes('not found')) {
        errorMessage = '存储桶不存在，请检查配置';
      } else if (error.message.includes('size')) {
        errorMessage = '文件大小超出限制';
      }

      return {
        success: false,
        error: errorMessage
      };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);

    return {
      success: true,
      url: urlData.publicUrl,
      path: data.path
    };
  } catch (error) {
    console.error('Upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
}

/**
 * Upload multiple files
 */
export async function uploadMultipleFiles(
  files: File[],
  bucket: string = STORAGE_BUCKETS.GAMES_SOURCE,
  folder?: string
): Promise<UploadResult[]> {
  const uploadPromises = files.map(file => uploadFile(file, bucket, folder));
  return Promise.all(uploadPromises);
}

/**
 * Delete a file from Supabase storage
 */
export async function deleteFile(
  path: string,
  bucket: string = STORAGE_BUCKETS.GAMES_SOURCE
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient();
    
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      return {
        success: false,
        error: error.message
      };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed'
    };
  }
}

/**
 * Get file URL from path
 */
export function getFileUrl(
  path: string,
  bucket: string = STORAGE_BUCKETS.GAMES_SOURCE
): string {
  const supabase = createClient();
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path);
  
  return data.publicUrl;
}

/**
 * Validate file type for product media
 */
export function validateMediaFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/mov'];
  const allowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'File type not supported. Please use JPG, PNG, WebP, MP4, WebM, or MOV files.'
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size too large. Maximum size is 10MB.'
    };
  }

  return { valid: true };
}

/**
 * Check if file is a video
 */
export function isVideoFile(file: File): boolean {
  return file.type.startsWith('video/');
}

/**
 * Check if file is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}
