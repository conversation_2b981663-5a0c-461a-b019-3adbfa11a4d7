import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { sql } from 'drizzle-orm';

async function testDatabase() {
  try {
    console.log('=== Testing Database Connection ===');
    console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL not found');
    }
    
    // Create connection
    const queryClient = postgres(process.env.DATABASE_URL, { max: 1 });
    const db = drizzle(queryClient);
    
    console.log('Connection created successfully');
    
    // Test basic query
    console.log('Testing basic query...');
    const result = await db.execute(sql`SELECT 1 as test`);
    console.log('Basic query result:', result);
    
    // Check if users table exists
    console.log('Checking users table...');
    const tableCheck = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      ) as exists;
    `);
    console.log('Table check result:', tableCheck);
    const usersTableExists = tableCheck[0]?.exists || false;
    console.log('Users table exists:', usersTableExists);
    
    // List all tables
    console.log('Listing all tables...');
    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);
    console.log('All tables:', tables.rows.map(r => r.table_name));
    
    // If users table exists, try to query it
    if (tableCheck.rows[0]?.exists) {
      console.log('Querying users table...');
      const userCount = await db.execute(sql`SELECT COUNT(*) FROM users`);
      console.log('User count:', userCount.rows[0]?.count);
      
      const users = await db.execute(sql`SELECT id, email, role FROM users LIMIT 3`);
      console.log('Sample users:', users.rows);
    }
    
    await queryClient.end();
    console.log('=== Test completed successfully ===');
    
  } catch (error) {
    console.error('=== Database Test Error ===');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

testDatabase();