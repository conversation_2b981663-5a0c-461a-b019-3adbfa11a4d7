import type { Metada<PERSON> } from "next";
import { <PERSON>eist } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { Toaster } from "sonner";
import "./globals.css";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  metadataBase: new URL(defaultUrl),
  title: "GameVault - Digital Game Codes Store",
  description: "Your ultimate destination for digital game codes with instant delivery. Browse games for Nintendo Switch, PlayStation, Xbox, Steam and more!",
  keywords: ["digital games", "game codes", "nintendo switch", "playstation", "xbox", "steam", "instant delivery"],
  authors: [{ name: "GameVault Team" }],
  creator: "GameVault",
  publisher: "GameVault",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: defaultUrl,
    title: "GameVault - Digital Game Codes Store",
    description: "Your ultimate destination for digital game codes with instant delivery.",
    siteName: "GameVault",
  },
  twitter: {
    card: "summary_large_image",
    title: "GameVault - Digital Game Codes Store",
    description: "Your ultimate destination for digital game codes with instant delivery.",
    creator: "@gamevault",
  },
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased`} suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
