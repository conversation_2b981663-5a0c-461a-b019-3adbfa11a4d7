import { Suspense } from 'react';
import { getProductsWithStock, getAvailablePlatforms, getAvailableRegions } from '@/lib/services/products';
import { ProductCard } from '@/components/products/product-card';
import { ProductFilters } from '@/components/products/product-filters';
import { ProductSearch } from '@/components/products/product-search';
import { Skeleton } from '@/components/ui/skeleton';

interface ProductsPageProps {
  searchParams: Promise<{
    platform?: string;
    region?: string;
    search?: string;
    page?: string;
  }>;
}

export default async function ProductsPage({ searchParams }: ProductsPageProps) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams.page || '1');
  const limit = 12;
  const offset = (page - 1) * limit;

  const [products, platforms, regions] = await Promise.all([
    getProductsWithStock(
      {
        platform: resolvedSearchParams.platform,
        region: resolvedSearchParams.region,
        search: resolvedSearchParams.search,
        status: 'active',
      },
      {
        limit,
        offset,
        sortBy: 'created_at',
        sortOrder: 'desc',
      }
    ),
    getAvailablePlatforms(),
    getAvailableRegions(resolvedSearchParams.platform),
  ]);

  return (
    <div className="container mx-auto px-4 py-8 gaming-bg-pattern min-h-screen">
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold mb-4 gaming-text-gradient">
          🎮 Digital Game Codes
        </h1>
        <p className="text-muted-foreground text-lg">
          Browse our collection of digital activation codes for your favorite gaming platforms.
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <div className="space-y-6 sticky top-8">
            <ProductSearch initialValue={resolvedSearchParams.search} />
            <ProductFilters
              platforms={platforms}
              regions={regions}
              selectedPlatform={resolvedSearchParams.platform}
              selectedRegion={resolvedSearchParams.region}
            />
          </div>
        </div>

        {/* Products Grid */}
        <div className="flex-1">
          <div className="mb-6 flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {products.length} products
            </p>
          </div>

          <Suspense fallback={<ProductsGridSkeleton />}>
            {products.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {products.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🎮</div>
                <h3 className="text-lg font-semibold mb-2">No products found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your filters or search terms.
                </p>
              </div>
            )}
          </Suspense>

          {/* Pagination */}
          {products.length === limit && (
            <div className="mt-8 flex justify-center">
              <div className="flex items-center space-x-2">
                {page > 1 && (
                  <a
                    href={`/products?${new URLSearchParams({
                      ...resolvedSearchParams,
                      page: (page - 1).toString(),
                    }).toString()}`}
                    className="px-4 py-2 border rounded-md hover:bg-muted"
                  >
                    Previous
                  </a>
                )}
                <span className="px-4 py-2">Page {page}</span>
                <a
                  href={`/products?${new URLSearchParams({
                    ...resolvedSearchParams,
                    page: (page + 1).toString(),
                  }).toString()}`}
                  className="px-4 py-2 border rounded-md hover:bg-muted"
                >
                  Next
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function ProductsGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="border rounded-lg p-4 space-y-4">
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <div className="flex justify-between items-center">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      ))}
    </div>
  );
}
