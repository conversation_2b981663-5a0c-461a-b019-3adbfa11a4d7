import { notFound } from 'next/navigation';
import Link from 'next/link';
import { getProductBySlug } from '@/lib/services/products';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatAmount } from '@/lib/utils/format';
import { ArrowLeft, ShoppingCart, Shield, Zap, Clock } from 'lucide-react';
import { ProductMediaGallery } from '@/components/products/product-media-gallery';
import { RichTextDisplay } from '@/components/ui/rich-text-display';

interface ProductPageProps {
  params: Promise<{
    slug: string;
  }>;
}

const platformIcons: Record<string, string> = {
  switch: '🎮',
  ps: '🎮',
  xbox: '🎮',
  steam: '💻',
  gift_card: '🎁',
};

const platformNames: Record<string, string> = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: 'Gift Card',
};

export default async function ProductPage({ params }: ProductPageProps) {
  const resolvedParams = await params;
  const product = await getProductBySlug(resolvedParams.slug);

  if (!product || product.status !== 'active') {
    notFound();
  }

  const isInStock = product.stockCount > 0;
  const isLowStock = product.stockCount > 0 && product.stockCount <= 5;

  return (
    <div className="container mx-auto px-4 py-8 gaming-bg-pattern min-h-screen">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Button variant="ghost" size="sm" asChild className="mb-4 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
          <Link href="/products" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Products
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Product Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <span className="text-4xl">
                {platformIcons[product.platform] || '🎮'}
              </span>
              <div>
                <Badge variant="secondary" className="mb-2">
                  {platformNames[product.platform] || product.platform.toUpperCase()}
                </Badge>
                <h1 className="text-3xl font-bold">{product.name}</h1>
                <p className="text-muted-foreground">Region: {product.region}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Badge variant={isInStock ? 'default' : 'destructive'}>
                {isInStock ? `${product.stockCount} in stock` : 'Out of stock'}
              </Badge>
              {isLowStock && (
                <Badge variant="outline" className="text-orange-600 border-orange-200">
                  ⚠️ Low stock
                </Badge>
              )}
            </div>
          </div>

          {/* Media Gallery */}
          <Card className="gaming-card border-0">
            <CardContent className="p-6">
              <ProductMediaGallery
                mainImageUrl={product.mainImageUrl}
                videoUrl={product.videoUrl}
                imageUrls={product.imageUrls}
                productName={product.name}
              />
            </CardContent>
          </Card>

          {/* Short Description */}
          {product.description && (
            <Card className="gaming-card border-0">
              <CardHeader>
                <CardTitle className="gaming-text-gradient">📖 Product Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {product.description}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Rich Text Content */}
          {product.detailContent && (
            <Card className="gaming-card border-0">
              <CardHeader>
                <CardTitle className="gaming-text-gradient">📋 Detailed Information</CardTitle>
              </CardHeader>
              <CardContent>
                <RichTextDisplay content={product.detailContent} />
              </CardContent>
            </Card>
          )}

          {/* Features */}
          <Card className="gaming-card border-0">
            <CardHeader>
              <CardTitle className="gaming-text-gradient">⭐ Why Choose Us?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3 p-4 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
                  <Zap className="h-8 w-8 text-blue-500" />
                  <div>
                    <h3 className="font-semibold">Instant Delivery</h3>
                    <p className="text-sm text-muted-foreground">
                      Get your code immediately after payment
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-4 rounded-lg bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30">
                  <Shield className="h-8 w-8 text-green-500" />
                  <div>
                    <h3 className="font-semibold">100% Authentic</h3>
                    <p className="text-sm text-muted-foreground">
                      All codes are sourced directly from publishers
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-4 rounded-lg bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/30 dark:to-pink-900/30">
                  <Clock className="h-8 w-8 text-purple-500" />
                  <div>
                    <h3 className="font-semibold">24/7 Support</h3>
                    <p className="text-sm text-muted-foreground">
                      Get help whenever you need it
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* How to Redeem */}
          <Card>
            <CardHeader>
              <CardTitle>How to Redeem Your Code</CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Complete your purchase and receive your activation code via email</li>
                <li>Open your {platformNames[product.platform] || product.platform} platform</li>
                <li>Navigate to the &quot;Redeem Code&quot; or &quot;Add Funds&quot; section</li>
                <li>Enter your activation code exactly as provided</li>
                <li>Follow the on-screen instructions to complete the redemption</li>
              </ol>
            </CardContent>
          </Card>
        </div>

        {/* Purchase Card */}
        <div className="lg:col-span-1">
          <Card className="gaming-card sticky top-8 border-0 gaming-glow">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="gaming-text-gradient">💳 Purchase</span>
                <div className="text-right">
                  <div className="text-2xl font-bold gaming-text-gradient">
                    {formatAmount(product.amountCents, product.currency)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    ⚡ Instant delivery
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Platform:</span>
                  <span>{platformNames[product.platform] || product.platform.toUpperCase()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Region:</span>
                  <span>{product.region}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery:</span>
                  <span className="text-green-600">Instant</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Stock:</span>
                  <span className={isInStock ? 'text-green-600' : 'text-red-600'}>
                    {isInStock ? `${product.stockCount} available` : 'Out of stock'}
                  </span>
                </div>
              </div>

              <div className="border-t pt-4">
                <Button
                  asChild
                  className={`w-full gaming-btn-primary ${!isInStock ? 'opacity-50 cursor-not-allowed' : ''}`}
                  size="lg"
                  disabled={!isInStock}
                >
                  <Link href={`/products/${product.slug || product.id}/purchase`}>
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {isInStock ? 'Buy Now' : 'Out of Stock'}
                  </Link>
                </Button>
              </div>

              <div className="text-xs text-muted-foreground text-center">
                <p>🔒 Secure payment powered by Stripe</p>
                <p>💳 All major credit cards accepted</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
