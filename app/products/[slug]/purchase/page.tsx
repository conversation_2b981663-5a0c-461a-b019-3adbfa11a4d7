import { notFound, redirect } from 'next/navigation';
import { getProductBySlug } from '@/lib/services/products';
import { createClient } from '@/lib/supabase/server';
import { CheckoutForm } from '@/components/checkout/checkout-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatAmount } from '@/lib/utils/format';
import { features } from '@/lib/config';
import { ArrowLeft, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface PurchasePageProps {
  params: Promise<{
    slug: string;
  }>;
}

const platformNames: Record<string, string> = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: 'Gift Card',
};

export default async function PurchasePage({ params }: PurchasePageProps) {
  const resolvedParams = await params;
  const product = await getProductBySlug(resolvedParams.slug);

  if (!product || !product.isActive) {
    notFound();
  }

  // Check if payments are enabled
  if (!features.payments) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Button variant="ghost" size="sm" asChild className="mb-4">
            <Link href={`/products/${resolvedParams.slug}`} className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Product
            </Link>
          </Button>

          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="text-center py-12">
              <AlertTriangle className="h-16 w-16 mx-auto text-orange-600 mb-4" />
              <h2 className="text-2xl font-bold mb-2 text-orange-800">Demo Mode</h2>
              <p className="text-orange-700 mb-6">
                Payments are disabled because Stripe is not configured yet.
              </p>
              <div className="bg-white p-4 rounded-lg border border-orange-200 mb-6">
                <h3 className="font-semibold text-orange-800 mb-2">To enable payments:</h3>
                <ol className="text-sm text-orange-700 text-left space-y-1">
                  <li>1. Create a Stripe account</li>
                  <li>2. Get your API keys from Stripe dashboard</li>
                  <li>3. Update STRIPE_SECRET_KEY and NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY in .env.local</li>
                  <li>4. Set up webhook endpoint for payment notifications</li>
                  <li>5. Restart the development server</li>
                </ol>
              </div>
              <div className="flex space-x-4 justify-center">
                <Button asChild variant="outline">
                  <Link href="/products">Browse Products</Link>
                </Button>
                <Button asChild>
                  <Link href="/SETUP_GUIDE.md" target="_blank">Setup Guide</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const supabase = await createClient();

  // Check if user is authenticated
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    redirect('/auth/login?redirect=' + encodeURIComponent(`/products/${resolvedParams.slug}/purchase`));
  }

  const isInStock = product.stockCount > 0;

  if (!isInStock) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Button variant="ghost" size="sm" asChild className="mb-4">
            <Link href={`/products/${resolvedParams.slug}`} className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Product
            </Link>
          </Button>
          
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-6xl mb-4">😔</div>
              <h2 className="text-2xl font-bold mb-2">Out of Stock</h2>
              <p className="text-muted-foreground mb-6">
                Sorry, this product is currently out of stock. Please check back later.
              </p>
              <Button asChild>
                <Link href="/products">Browse Other Products</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <Button variant="ghost" size="sm" asChild className="mb-6">
          <Link href={`/products/${resolvedParams.slug}`} className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Product
          </Link>
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="text-3xl">
                    {product.platform === 'switch' && '🎮'}
                    {product.platform === 'ps' && '🎮'}
                    {product.platform === 'xbox' && '🎮'}
                    {product.platform === 'steam' && '💻'}
                    {product.platform === 'gift_card' && '🎁'}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">{product.name}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="secondary">
                        {platformNames[product.platform] || product.platform.toUpperCase()}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        Region: {product.region}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span>Product Price:</span>
                    <span>{formatAmount(product.amountCents, product.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Processing Fee:</span>
                    <span>$0.00</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg border-t pt-2">
                    <span>Total:</span>
                    <span>{formatAmount(product.amountCents, product.currency)}</span>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-sm mb-2">What you&apos;ll receive:</h4>
                  <ul className="text-sm space-y-1">
                    <li>✅ Digital activation code</li>
                    <li>✅ Instant email delivery</li>
                    <li>✅ Step-by-step redemption guide</li>
                    <li>✅ 24/7 customer support</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Checkout Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Payment Details</CardTitle>
              </CardHeader>
              <CardContent>
                <CheckoutForm 
                  product={product}
                  user={user}
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center space-x-2 text-sm text-muted-foreground">
            <span>🔒</span>
            <span>Secure payment powered by Stripe</span>
            <span>•</span>
            <span>Your payment information is encrypted and secure</span>
          </div>
        </div>
      </div>
    </div>
  );
}
