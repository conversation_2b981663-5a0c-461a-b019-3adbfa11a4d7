import { redirect } from 'next/navigation';
import Link from 'next/link';
import { createClient } from '@/lib/supabase/server';
import { getOrdersByUserId } from '@/lib/services/orders';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatAmount } from '@/lib/utils/format';
import { ShoppingBag, Eye } from 'lucide-react';

interface OrdersPageProps {
  searchParams: Promise<{
    page?: string;
  }>;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-blue-100 text-blue-800',
  delivered: 'bg-green-100 text-green-800',
  refunded: 'bg-red-100 text-red-800',
  under_review: 'bg-orange-100 text-orange-800',
};

const platformNames: Record<string, string> = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: 'Gift Card',
};

export default async function OrdersPage({ searchParams }: OrdersPageProps) {
  const supabase = await createClient();

  // Check if user is authenticated
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    redirect('/auth/login');
  }

  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams.page || '1');
  const limit = 10;
  const offset = (page - 1) * limit;

  const orders = await getOrdersByUserId(user.id, limit, offset);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">My Orders</h1>
          <p className="text-muted-foreground">
            View and manage your digital game code purchases.
          </p>
        </div>

        {orders.length > 0 ? (
          <div className="space-y-6">
            {orders.map((order) => (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">
                        Order #{order.id.slice(-8)}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.createdAt).toLocaleDateString()} at{' '}
                        {new Date(order.createdAt).toLocaleTimeString()}
                      </p>
                    </div>
                    <Badge className={statusColors[order.status]}>
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('_', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="text-2xl">
                        {order.product.platform === 'switch' && '🎮'}
                        {order.product.platform === 'ps' && '🎮'}
                        {order.product.platform === 'xbox' && '🎮'}
                        {order.product.platform === 'steam' && '💻'}
                        {order.product.platform === 'gift_card' && '🎁'}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold">{order.product.name}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {platformNames[order.product.platform] || order.product.platform.toUpperCase()}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {order.product.region}
                          </span>
                        </div>
                        <div className="mt-2">
                          <span className="font-semibold">
                            {formatAmount(order.amountCents, order.currency)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col space-y-2">
                      <Button asChild size="sm" variant="outline">
                        <Link href={`/orders/${order.id}`}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Link>
                      </Button>
                      {order.status === 'delivered' && (
                        <Badge variant="default" className="text-xs text-center">
                          ✅ Code Ready
                        </Badge>
                      )}
                      {order.status === 'under_review' && (
                        <Badge variant="outline" className="text-xs text-center text-orange-600">
                          ⏳ Under Review
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Pagination */}
            {orders.length === limit && (
              <div className="flex justify-center mt-8">
                <div className="flex items-center space-x-2">
                  {page > 1 && (
                    <Button asChild variant="outline">
                      <Link href={`/orders?page=${page - 1}`}>
                        Previous
                      </Link>
                    </Button>
                  )}
                  <span className="px-4 py-2">Page {page}</span>
                  <Button asChild variant="outline">
                    <Link href={`/orders?page=${page + 1}`}>
                      Next
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-6xl mb-4">
                <ShoppingBag className="h-16 w-16 mx-auto text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No orders yet</h3>
              <p className="text-muted-foreground mb-6">
                You haven&apos;t made any purchases yet. Browse our products to get started!
              </p>
              <Button asChild>
                <Link href="/products">Browse Products</Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
