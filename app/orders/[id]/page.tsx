import { notFound, redirect } from 'next/navigation';
import { createClient } from '@/lib/supabase/server';
import { getOrderById } from '@/lib/services/orders';
import { getActivationCodeByOrderId } from '@/lib/services/activation-codes';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatAmount } from '@/lib/utils/format';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { ActivationCodeDisplay } from '@/components/orders/activation-code-display';

interface OrderPageProps {
  params: Promise<{
    id: string;
  }>;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-blue-100 text-blue-800',
  delivered: 'bg-green-100 text-green-800',
  refunded: 'bg-red-100 text-red-800',
  under_review: 'bg-orange-100 text-orange-800',
};

const platformNames: Record<string, string> = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: 'Gift Card',
};

export default async function OrderPage({ params }: OrderPageProps) {
  const supabase = await createClient();

  // Check if user is authenticated
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    redirect('/auth/login');
  }

  const resolvedParams = await params;
  const order = await getOrderById(resolvedParams.id);

  if (!order) {
    notFound();
  }

  // Check if user owns this order
  if (order.userId !== user.id) {
    notFound();
  }

  // Get activation code if order is delivered
  const activationCode = order.status === 'delivered' 
    ? await getActivationCodeByOrderId(order.id)
    : null;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <Button variant="ghost" size="sm" asChild className="mb-6">
          <Link href="/orders" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Link>
        </Button>

        <div className="space-y-6">
          {/* Order Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl">Order #{order.id.slice(-8)}</CardTitle>
                  <p className="text-muted-foreground">
                    Placed on {new Date(order.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <Badge className={statusColors[order.status]}>
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('_', ' ')}
                </Badge>
              </div>
            </CardHeader>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Product Details */}
            <Card>
              <CardHeader>
                <CardTitle>Product Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="text-3xl">
                    {order.product.platform === 'switch' && '🎮'}
                    {order.product.platform === 'ps' && '🎮'}
                    {order.product.platform === 'xbox' && '🎮'}
                    {order.product.platform === 'steam' && '💻'}
                    {order.product.platform === 'gift_card' && '🎁'}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">{order.product.name}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="secondary">
                        {platformNames[order.product.platform] || order.product.platform.toUpperCase()}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        Region: {order.product.region}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span>Product Price:</span>
                    <span>{formatAmount(order.amountCents, order.currency)}</span>
                  </div>
                  <div className="flex justify-between font-semibold">
                    <span>Total Paid:</span>
                    <span>{formatAmount(order.amountCents, order.currency)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Status */}
            <Card>
              <CardHeader>
                <CardTitle>Order Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Order Placed</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {order.paidAt && (
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="font-medium">Payment Confirmed</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(order.paidAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  )}

                  {order.deliveredAt && (
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="font-medium">Code Delivered</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(order.deliveredAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  )}

                  {order.status === 'under_review' && (
                    <div className="bg-orange-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-sm mb-2">⏳ Order Under Review</h4>
                      <p className="text-sm">
                        Your order is being reviewed for security purposes.
                        This typically takes 24 hours. You&apos;ll receive an email once approved.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Activation Code */}
          {activationCode && (
            <Card>
              <CardHeader>
                <CardTitle>Your Activation Code</CardTitle>
              </CardHeader>
              <CardContent>
                <ActivationCodeDisplay code={activationCode.code} />
                
                <div className="mt-6 bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-sm mb-2">How to Redeem:</h4>
                  <ol className="text-sm space-y-1 list-decimal list-inside">
                    <li>Open your {platformNames[order.product.platform]} platform</li>
                    <li>Navigate to the &quot;Redeem Code&quot; section</li>
                    <li>Enter the activation code above</li>
                    <li>Follow the on-screen instructions</li>
                  </ol>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Support */}
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                If you have any issues with your order or activation code, our support team is here to help.
              </p>
              <div className="flex space-x-4">
                <Button variant="outline" size="sm">
                  Contact Support
                </Button>
                <Button variant="outline" size="sm">
                  Report Issue
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
