@tailwind base;
@tailwind components;
@tailwind utilities;

/* Gaming Theme Variables */
:root {
  /* Gaming Color Palette */
  --gaming-primary: #6366f1; /* Electric blue */
  --gaming-primary-dark: #4f46e5;
  --gaming-secondary: #8b5cf6; /* Purple */
  --gaming-accent: #06b6d4; /* Cyan */
  --gaming-success: #10b981; /* Green */
  --gaming-warning: #f59e0b; /* Amber */
  --gaming-danger: #ef4444; /* Red */

  /* Gaming Gradients */
  --gaming-gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gaming-gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --gaming-gradient-accent: linear-gradient(135deg, #10b981 0%, #059669 100%);

  /* Gaming Shadows */
  --gaming-shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
  --gaming-shadow-card: 0 4px 20px rgba(0, 0, 0, 0.1);
  --gaming-shadow-hover: 0 8px 30px rgba(99, 102, 241, 0.2);
}

/* Dark theme gaming colors */
.dark {
  --gaming-bg-primary: #0f0f23;
  --gaming-bg-secondary: #1a1a2e;
  --gaming-bg-card: #16213e;
  --gaming-text-primary: #e2e8f0;
  --gaming-text-secondary: #94a3b8;
}

/* Gaming Component Styles */
@layer components {
  /* Gaming Card */
  .gaming-card {
    @apply bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
    background: linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%);
  }

  .dark .gaming-card {
    background: linear-gradient(145deg, rgba(30,41,59,0.95) 0%, rgba(51,65,85,0.95) 100%);
  }

  /* Gaming Button */
  .gaming-btn-primary {
    @apply bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .gaming-btn-secondary {
    @apply bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  /* Gaming Badge */
  .gaming-badge-platform {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-sm;
  }

  .gaming-badge-stock {
    @apply inline-flex items-center px-2 py-1 rounded-md text-xs font-medium;
  }

  .gaming-badge-stock.in-stock {
    @apply bg-gradient-to-r from-green-500 to-emerald-500 text-white;
  }

  .gaming-badge-stock.low-stock {
    @apply bg-gradient-to-r from-amber-500 to-orange-500 text-white;
  }

  .gaming-badge-stock.out-of-stock {
    @apply bg-gradient-to-r from-red-500 to-rose-500 text-white;
  }

  /* Gaming Glow Effect */
  .gaming-glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .gaming-glow-hover:hover {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
  }

  /* Gaming Text Gradient */
  .gaming-text-gradient {
    @apply bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent;
  }

  /* Gaming Background Pattern */
  .gaming-bg-pattern {
    background: linear-gradient(135deg, #fafafa 0%, #f8fafc 50%, #f1f5f9 100%);
    background-image:
      radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  }

  .dark .gaming-bg-pattern {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    background-image:
      radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  }

  /* Gaming Animations */
  @keyframes gaming-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  @keyframes gaming-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
  }

  @keyframes gaming-glow-pulse {
    0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
    50% { box-shadow: 0 0 30px rgba(99, 102, 241, 0.5); }
  }

  .gaming-pulse {
    animation: gaming-pulse 2s ease-in-out infinite;
  }

  .gaming-float {
    animation: gaming-float 3s ease-in-out infinite;
  }

  .gaming-glow-pulse {
    animation: gaming-glow-pulse 2s ease-in-out infinite;
  }

  /* Gaming Hover Effects */
  .gaming-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .gaming-card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.2);
  }

  /* Gaming Loading Spinner */
  @keyframes gaming-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .gaming-spin {
    animation: gaming-spin 1s linear infinite;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
