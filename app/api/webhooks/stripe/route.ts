import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { verifyWebhookSignature, assessPaymentRisk } from '@/lib/services/stripe';
import { getOrderByPaymentIntentId, updateOrderStatus, updateOrderRiskLevel } from '@/lib/services/orders';
import { allocateActivationCode } from '@/lib/services/activation-codes';
import { getProductById } from '@/lib/services/products';
import type { SelectProduct } from '@/lib/db/schema';
import { sendOrderConfirmationEmail, sendOrderReceiptEmail, sendOrderUnderReviewEmail } from '@/lib/services/email';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import type Stripe from 'stripe';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature')!;

    // Verify webhook signature
    const event = verifyWebhookSignature(body, signature, webhookSecret);

    console.log('Stripe webhook event:', event.type);

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 400 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Get order by payment intent ID
    const order = await getOrderByPaymentIntentId(paymentIntent.id);
    if (!order) {
      console.error('Order not found for payment intent:', paymentIntent.id);
      return;
    }

    // Get product and user details
    const [product, userResult] = await Promise.all([
      getProductById(order.productId),
      db.select().from(users).where(eq(users.id, order.userId)).limit(1),
    ]);

    if (!product) {
      console.error('Product not found:', order.productId);
      return;
    }

    if (userResult.length === 0) {
      console.error('User not found:', order.userId);
      return;
    }

    const user = userResult[0];

    // Assess payment risk
    const riskAssessment = await assessPaymentRisk(paymentIntent);
    
    // Update order risk level
    await updateOrderRiskLevel(order.id, riskAssessment.riskLevel);

    // Update order status to paid
    await updateOrderStatus(order.id, 'paid', undefined, 'system');

    // Send receipt email
    await sendOrderReceiptEmail({
      order,
      product,
      userEmail: user.email,
    });

    // Handle based on risk level
    if (riskAssessment.riskLevel === 'high') {
      // High risk - put under review
      await updateOrderStatus(order.id, 'under_review', undefined, 'system');
      
      // Send under review email
      await sendOrderUnderReviewEmail({
        order,
        product,
        userEmail: user.email,
      });

      console.log(`Order ${order.id} marked for review due to high risk:`, riskAssessment.reasons);
    } else {
      // Low/medium risk - proceed with automatic delivery
      await processOrderDelivery(order.id, product, user.email);
    }

  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Get order by payment intent ID
    const order = await getOrderByPaymentIntentId(paymentIntent.id);
    if (!order) {
      console.error('Order not found for payment intent:', paymentIntent.id);
      return;
    }

    // Update order status to failed (we can add this status to the enum if needed)
    // For now, we'll leave it as pending since the user might retry
    console.log(`Payment failed for order ${order.id}`);

  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function processOrderDelivery(orderId: string, product: SelectProduct, userEmail: string) {
  try {
    // Allocate activation code
    const deliveryResult = await allocateActivationCode(orderId, product.id);

    if (deliveryResult.success && deliveryResult.activationCode) {
      // Update order status to delivered
      await updateOrderStatus(orderId, 'delivered', undefined, 'system');

      // Send confirmation email with activation code
      await sendOrderConfirmationEmail({
        order: { id: orderId, productId: product.id, userId: '', currency: 'USD', amountCents: 0, status: 'delivered' as const, riskLevel: 'low' as const, paymentIntentId: '', metadata: {}, createdAt: new Date(), updatedAt: new Date(), paidAt: new Date(), deliveredAt: new Date(), refundedAt: null },
        product,
        userEmail,
        activationCode: deliveryResult.activationCode,
      });

      console.log(`Order ${orderId} delivered successfully`);
    } else {
      console.error(`Failed to deliver order ${orderId}:`, deliveryResult.error);
      
      // TODO: Add to retry queue or alert administrators
      // For now, we'll leave the order as paid but not delivered
    }

  } catch (error) {
    console.error('Error processing order delivery:', error);
  }
}
