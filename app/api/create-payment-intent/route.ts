import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getProductById } from '@/lib/services/products';
import { createOrder } from '@/lib/services/orders';
import { createPaymentIntent } from '@/lib/services/stripe';
import { hasStock } from '@/lib/services/activation-codes';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { productId, amount, currency, billingDetails } = body;

    // Validate required fields
    if (!productId || !amount || !currency) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get product details
    const product = await getProductById(productId);
    if (!product || !product.isActive) {
      return NextResponse.json(
        { error: 'Product not found or inactive' },
        { status: 404 }
      );
    }

    // Verify product has stock
    const inStock = await hasStock(productId);
    if (!inStock) {
      return NextResponse.json(
        { error: 'Product out of stock' },
        { status: 400 }
      );
    }

    // Verify amount matches product price
    if (amount !== product.amountCents) {
      return NextResponse.json(
        { error: 'Amount mismatch' },
        { status: 400 }
      );
    }

    // Create Stripe Payment Intent
    const paymentIntent = await createPaymentIntent({
      amount: product.amountCents,
      currency: product.currency,
      productId: product.id,
      userId: user.id,
      userEmail: user.email!,
      metadata: {
        productName: product.name,
        platform: product.platform,
        region: product.region,
        billingName: billingDetails?.name || '',
      },
    });

    // Create order in database
    const order = await createOrder({
      userId: user.id,
      productId: product.id,
      paymentIntentId: paymentIntent.id,
      currency: product.currency,
      amountCents: product.amountCents,
      metadata: {
        productName: product.name,
        platform: product.platform,
        region: product.region,
        billingDetails,
      },
    });

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      orderId: order.id,
    });

  } catch (error) {
    console.error('Create payment intent error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
