import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

export async function GET() {
  try {
    console.log('=== Testing Database Connection ===');
    
    // Test 1: Basic connection
    console.log('Testing basic connection...');
    const result = await db.execute(sql`SELECT 1 as test`);
    console.log('Basic connection successful:', result);
    
    // Test 2: Check if users table exists
    console.log('Checking if users table exists...');
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);
    console.log('Users table exists check:', tableExists);
    
    // Test 3: Try to query users table (if it exists)
    if (tableExists.rows[0]?.exists) {
      console.log('Attempting to query users table...');
      const userCount = await db.execute(sql`SELECT COUNT(*) FROM users`);
      console.log('Users count:', userCount);
      
      // Test 4: Try to select all users
      console.log('Attempting to select all users...');
      const allUsers = await db.select().from(users).limit(5);
      console.log('All users (first 5):', allUsers);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      basicConnection: true,
      usersTableExists: tableExists.rows[0]?.exists || false,
      tests: {
        basicConnection: result,
        tableExists: tableExists,
      }
    });
    
  } catch (error) {
    console.error('=== Database Connection Error ===', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}