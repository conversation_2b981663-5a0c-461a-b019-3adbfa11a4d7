import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check configuration status on server side where we have access to all env vars
    const configStatus = {
      database: <PERSON><PERSON><PERSON>(
        process.env.DATABASE_URL && 
        !process.env.DATABASE_URL.includes('[YOUR-PASSWORD]')
      ),
      stripe: <PERSON><PERSON><PERSON>(
        process.env.STRIPE_SECRET_KEY && 
        !process.env.STRIPE_SECRET_KEY.includes('your-')
      ),
      email: <PERSON><PERSON><PERSON>(
        process.env.RESEND_API_KEY && 
        !process.env.RESEND_API_KEY.includes('your-')
      ),
    };

    return NextResponse.json(configStatus);
  } catch (error) {
    console.error('Error checking config status:', error);
    return NextResponse.json(
      { error: 'Failed to check configuration status' },
      { status: 500 }
    );
  }
}
