import { NextRequest, NextResponse } from 'next/server';
import { requireAdminAccess, logAdminAction } from '@/lib/services/admin';
import { updateOrderStatus } from '@/lib/services/orders';
import { allocateActivationCode } from '@/lib/services/activation-codes';
import { getAdminOrderDetail } from '@/lib/services/admin-orders';
import { sendOrderReceiptEmail } from '@/lib/services/email';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify admin access
    const adminUser = await requireAdminAccess();
    const resolvedParams = await params;
    const { action } = await request.json();

    // Get order details
    const order = await getAdminOrderDetail(resolvedParams.id);
    if (!order) {
      return NextResponse.json(
        { success: false, error: '订单不存在' },
        { status: 404 }
      );
    }

    let result;

    switch (action) {
      case 'mark_paid':
        if (order.status !== 'pending') {
          return NextResponse.json(
            { success: false, error: '只能将待支付订单标记为已支付' },
            { status: 400 }
          );
        }
        
        result = await updateOrderStatus(order.id, 'paid', adminUser.id, 'admin');
        await logAdminAction(adminUser.id, 'order_marked_paid', {
          orderId: order.id,
          previousStatus: order.status,
        });
        break;

      case 'mark_delivered':
        if (order.status !== 'paid') {
          return NextResponse.json(
            { success: false, error: '只能将已支付订单标记为已发货' },
            { status: 400 }
          );
        }

        // Try to allocate activation code
        const deliveryResult = await allocateActivationCode(
          order.id,
          order.productId,
          adminUser.id
        );

        if (!deliveryResult.success) {
          return NextResponse.json(
            { success: false, error: `发货失败: ${deliveryResult.error}` },
            { status: 400 }
          );
        }

        result = await updateOrderStatus(order.id, 'delivered', adminUser.id, 'admin');
        
        // Send email notification
        try {
          await sendOrderReceiptEmail(order.userEmail, order, order.productName);
        } catch (emailError) {
          console.error('Failed to send delivery email:', emailError);
          // Don't fail the entire operation if email fails
        }

        await logAdminAction(adminUser.id, 'order_marked_delivered', {
          orderId: order.id,
          previousStatus: order.status,
          activationCodeId: deliveryResult.activationCode?.id,
        });
        break;

      case 'mark_under_review':
        if (!['pending', 'paid'].includes(order.status)) {
          return NextResponse.json(
            { success: false, error: '只能将待支付或已支付订单标记为审核中' },
            { status: 400 }
          );
        }

        result = await updateOrderStatus(order.id, 'under_review', adminUser.id, 'admin');
        await logAdminAction(adminUser.id, 'order_marked_under_review', {
          orderId: order.id,
          previousStatus: order.status,
        });
        break;

      case 'refund':
        if (!['paid', 'delivered'].includes(order.status)) {
          return NextResponse.json(
            { success: false, error: '只能退款已支付或已发货的订单' },
            { status: 400 }
          );
        }

        // TODO: Implement actual refund logic with Stripe
        result = await updateOrderStatus(order.id, 'refunded', adminUser.id, 'admin');
        await logAdminAction(adminUser.id, 'order_refunded', {
          orderId: order.id,
          previousStatus: order.status,
          refundAmount: order.amountCents,
        });
        break;

      case 'resend_code':
        if (order.status !== 'delivered' || !order.activationCode) {
          return NextResponse.json(
            { success: false, error: '只能重新发送已发货订单的激活码' },
            { status: 400 }
          );
        }

        // Resend email with activation code
        try {
          await sendOrderReceiptEmail(order.userEmail, order, order.productName);
          await logAdminAction(adminUser.id, 'activation_code_resent', {
            orderId: order.id,
            activationCodeId: order.activationCode.id,
          });
          result = { success: true };
        } catch (emailError) {
          console.error('Failed to resend activation code:', emailError);
          return NextResponse.json(
            { success: false, error: '重新发送激活码失败' },
            { status: 500 }
          );
        }
        break;

      default:
        return NextResponse.json(
          { success: false, error: '未知操作' },
          { status: 400 }
        );
    }

    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('Admin order action error:', error);
    
    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { success: false, error: '需要管理员权限' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: '操作失败' },
      { status: 500 }
    );
  }
}
