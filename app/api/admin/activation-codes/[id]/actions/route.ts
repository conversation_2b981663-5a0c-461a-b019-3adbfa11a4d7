import { NextRequest, NextResponse } from 'next/server';
import { requireAdminAccess, logAdminAction } from '@/lib/services/admin';
import { markCodeAsInvalid } from '@/lib/services/activation-codes';
import { db } from '@/lib/db';
import { activationCodes } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify admin access
    const adminUser = await requireAdminAccess();
    const resolvedParams = await params;
    const { action } = await request.json();

    // Get activation code
    const codeResult = await db
      .select()
      .from(activationCodes)
      .where(eq(activationCodes.id, resolvedParams.id))
      .limit(1);

    if (!codeResult.length) {
      return NextResponse.json(
        { success: false, error: '激活码不存在' },
        { status: 404 }
      );
    }

    const code = codeResult[0];
    let result;

    switch (action) {
      case 'mark_invalid':
        if (!['unused', 'locked'].includes(code.status)) {
          return NextResponse.json(
            { success: false, error: '只能将未使用或已锁定的激活码标记为无效' },
            { status: 400 }
          );
        }

        result = await markCodeAsInvalid(code.id, adminUser.id, '管理员手动标记');
        await logAdminAction(adminUser.id, 'activation_code_marked_invalid', {
          activationCodeId: code.id,
          previousStatus: code.status,
        });
        break;

      case 'reactivate':
        if (code.status !== 'invalid') {
          return NextResponse.json(
            { success: false, error: '只能重新激活无效的激活码' },
            { status: 400 }
          );
        }

        result = await db
          .update(activationCodes)
          .set({
            status: 'unused',
            updatedAt: new Date(),
          })
          .where(eq(activationCodes.id, code.id))
          .returning();

        await logAdminAction(adminUser.id, 'activation_code_reactivated', {
          activationCodeId: code.id,
          previousStatus: code.status,
        });
        break;

      default:
        return NextResponse.json(
          { success: false, error: '未知操作' },
          { status: 400 }
        );
    }

    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('Admin activation code action error:', error);
    
    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { success: false, error: '需要管理员权限' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: '操作失败' },
      { status: 500 }
    );
  }
}
