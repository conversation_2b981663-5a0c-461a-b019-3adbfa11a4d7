import { NextRequest, NextResponse } from 'next/server';
import { requireAdminAccess } from '@/lib/services/admin';
import { getAdminProducts, AdminProductFilters } from '@/lib/services/products';
import { db } from '@/lib/db';
import { products } from '@/lib/db/schema';

export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    await requireAdminAccess();

    // Parse search parameters
    const { searchParams } = new URL(request.url);

    const filters: AdminProductFilters = {
      search: searchParams.get('search') || undefined,
      status: searchParams.get('status') || undefined,
      platform: searchParams.get('platform') || undefined,
      category: searchParams.get('category') || undefined,
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 20,
    };

    const result = await getAdminProducts(filters);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Admin products API error:', error);

    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { success: false, error: '需要管理员权限' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: '获取商品列表失败' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    await requireAdminAccess();

    const body = await request.json();
    const {
      name,
      description,
      detailContent,
      platform,
      category,
      region,
      currency,
      amountCents,
      isActive,
      mainImageUrl,
      videoUrl,
      imageUrls,
      systemRequirements,
      features,
      tags,
    } = body;

    // Validate required fields
    if (!name || !platform || !region || !amountCents) {
      return NextResponse.json(
        { success: false, error: '缺少必填字段' },
        { status: 400 }
      );
    }

    // Generate slug from name
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fff]+/g, '-') // Support Chinese characters
      .replace(/(^-|-$)/g, '');

    // Create product
    const result = await db.insert(products).values({
      name,
      description,
      detailContent,
      platform,
      category,
      region,
      currency: currency || 'CNY',
      amountCents: parseInt(amountCents),
      isActive: isActive !== false, // Default to true
      slug,
      mainImageUrl,
      videoUrl,
      imageUrls: imageUrls || [],
      systemRequirements,
      features: features || [],
      tags: tags || [],
    }).returning();

    const newProduct = result[0];

    return NextResponse.json({
      success: true,
      data: newProduct
    });

  } catch (error) {
    console.error('Create product API error:', error);

    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { success: false, error: '需要管理员权限' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: '创建商品失败' },
      { status: 500 }
    );
  }
}
