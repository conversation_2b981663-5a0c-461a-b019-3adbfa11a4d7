import { NextRequest, NextResponse } from 'next/server';
import { getCurrentAdminUser } from '@/lib/services/admin';
import { db } from '@/lib/db';
import { products } from '@/lib/db/schema';
import postgres from 'postgres';

// Static options that don't change frequently
const STATIC_OPTIONS = {
  statuses: [
    { value: 'active', label: '在售' },
    { value: 'inactive', label: '下架' },
  ],
  currencies: [
    { value: 'USD', label: '美元 (USD)' },
    { value: 'CNY', label: '人民币 (CNY)' },
    { value: 'EUR', label: '欧元 (EUR)' },
  ],
  regions: [
    { value: 'Global', label: '全球' },
    { value: 'US', label: '美国' },
    { value: 'CN', label: '中国' },
    { value: 'EU', label: '欧洲' },
  ],
  // Since database doesn't have category field, provide static categories
  categories: [
    { value: 'action', label: '动作游戏' },
    { value: 'adventure', label: '冒险游戏' },
    { value: 'rpg', label: '角色扮演' },
    { value: 'strategy', label: '策略游戏' },
    { value: 'simulation', label: '模拟游戏' },
    { value: 'sports', label: '体育游戏' },
    { value: 'racing', label: '竞速游戏' },
    { value: 'puzzle', label: '益智游戏' },
    { value: 'indie', label: '独立游戏' },
    { value: 'horror', label: '恐怖游戏' },
    { value: 'fighting', label: '格斗游戏' },
    { value: 'shooter', label: '射击游戏' },
    { value: 'gift_card', label: '礼品卡' },
  ],
};

// Platform labels mapping
const PLATFORM_LABELS: Record<string, string> = {
  steam: 'Steam',
  ps: 'PlayStation',
  xbox: 'Xbox',
  switch: 'Nintendo Switch',
  gift_card: '礼品卡',
};

/**
 * Get dynamic options from database
 */
async function getDynamicOptions() {
  try {
    // Create a postgres client for raw SQL queries
    const queryClient = postgres(process.env.DATABASE_URL!);

    // Get unique platforms from products using raw SQL to avoid enum issues
    const platformsResult = await queryClient`
      SELECT DISTINCT platform FROM products
      WHERE platform IS NOT NULL
    `;

    await queryClient.end();

    const platforms = platformsResult
      .map((p: any) => ({
        value: p.platform,
        label: PLATFORM_LABELS[p.platform] || p.platform,
      }))
      .filter(p => p.value);

    return {
      platforms,
    };
  } catch (error) {
    console.error('Error getting dynamic options:', error);
    // Return fallback static options
    return {
      platforms: [
        { value: 'steam', label: 'Steam' },
        { value: 'ps', label: 'PlayStation' },
        { value: 'xbox', label: 'Xbox' },
        { value: 'switch', label: 'Nintendo Switch' },
        { value: 'gift_card', label: '礼品卡' },
      ],
    };
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const adminUser = await getCurrentAdminUser();
    if (!adminUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get dynamic options from database
    const dynamicOptions = await getDynamicOptions();

    // Combine static and dynamic options
    const productOptions = {
      ...STATIC_OPTIONS,
      ...dynamicOptions,
    };

    return NextResponse.json({
      success: true,
      data: productOptions,
    });
  } catch (error) {
    console.error('Error fetching product options:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
