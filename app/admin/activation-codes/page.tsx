import { Suspense } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { AdminActivationCodesTable } from '@/components/admin/admin-activation-codes-table';
import { AdminActivationCodesFilters } from '@/components/admin/admin-activation-codes-filters';
import { AdminActivationCodesStats } from '@/components/admin/admin-activation-codes-stats';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

interface ActivationCodesPageProps {
  searchParams: Promise<{
    page?: string;
    product?: string;
    status?: string;
    search?: string;
  }>;
}

export default async function AdminActivationCodesPage({ searchParams }: ActivationCodesPageProps) {
  const params = await searchParams;
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">激活码管理</h1>
          <p className="text-gray-600">管理所有商品的激活码库存</p>
        </div>
        <Button asChild>
          <Link href="/admin/activation-codes/import">
            <Plus className="mr-2 h-4 w-4" />
            批量导入
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <Suspense fallback={<StatsCardsSkeleton />}>
        <AdminActivationCodesStats />
      </Suspense>

      <Card>
        <CardHeader>
          <CardTitle>筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminActivationCodesFilters />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>激活码列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<TableSkeleton />}>
            <AdminActivationCodesTable searchParams={params} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

function StatsCardsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-24" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function TableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-8 w-24" />
      </div>
      <div className="space-y-2">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border rounded">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 flex-1" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-24" />
          </div>
        ))}
      </div>
    </div>
  );
}
