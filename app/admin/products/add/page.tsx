import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { AdminAddProductForm } from '@/components/admin/admin-add-product-form';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function AdminAddProductPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/products">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回商品列表
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">添加商品</h1>
          <p className="text-gray-600 mt-1">创建新的游戏商品</p>
        </div>
      </div>

      {/* Add Product Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>商品信息</CardTitle>
          </CardHeader>
          <CardContent>
            <AdminAddProductForm />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
