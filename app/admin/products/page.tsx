import { Suspense } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AdminProductsTable } from '@/components/admin/admin-products-table';
import { AdminProductsFilters } from '@/components/admin/admin-products-filters';
import { AdminProductsStats } from '@/components/admin/admin-products-stats';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus } from 'lucide-react';
import Link from 'next/link';

interface AdminProductsPageProps {
  searchParams: {
    page?: string;
    status?: string;
    platform?: string;
    search?: string;
    category?: string;
  };
}

export default async function AdminProductsPage({ searchParams }: AdminProductsPageProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">商品管理</h1>
          <p className="text-gray-600 mt-1">管理游戏商品和库存</p>
        </div>
        <Button asChild>
          <Link href="/admin/products/add">
            <Plus className="h-4 w-4 mr-2" />
            添加商品
          </Link>
        </Button>
      </div>

      {/* Stats */}
      <Suspense fallback={
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      }>
        <AdminProductsStats />
      </Suspense>

      {/* Filters */}
      <Card className="border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800">筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminProductsFilters />
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card className="border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800">商品列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          }>
            <AdminProductsTable searchParams={searchParams} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
