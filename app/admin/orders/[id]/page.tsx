import { notFound } from 'next/navigation';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdminOrderActions } from '@/components/admin/admin-order-actions';
import { getAdminOrderDetail } from '@/lib/services/admin-orders';
import { formatCurrency } from '@/lib/utils';

interface OrderDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-blue-100 text-blue-800',
  delivered: 'bg-green-100 text-green-800',
  refunded: 'bg-red-100 text-red-800',
  under_review: 'bg-orange-100 text-orange-800',
};

const statusLabels = {
  pending: '待支付',
  paid: '已支付',
  delivered: '已发货',
  refunded: '已退款',
  under_review: '审核中',
};

const riskColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-red-100 text-red-800',
};

const riskLabels = {
  low: '低风险',
  medium: '中风险',
  high: '高风险',
};

export default async function AdminOrderDetailPage({ params }: OrderDetailPageProps) {
  const resolvedParams = await params;
  const order = await getAdminOrderDetail(resolvedParams.id);

  if (!order) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">订单详情</h1>
          <p className="text-gray-600">订单ID: {order.id}</p>
        </div>
        <AdminOrderActions order={order} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Order Information */}
        <Card>
          <CardHeader>
            <CardTitle>订单信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">订单状态</label>
                <div className="mt-1">
                  <Badge 
                    className={statusColors[order.status as keyof typeof statusColors]}
                    variant="secondary"
                  >
                    {statusLabels[order.status as keyof typeof statusLabels]}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">风险等级</label>
                <div className="mt-1">
                  <Badge 
                    className={riskColors[order.riskLevel as keyof typeof riskColors]}
                    variant="secondary"
                  >
                    {riskLabels[order.riskLevel as keyof typeof riskLabels]}
                  </Badge>
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">订单金额</label>
              <div className="mt-1 text-lg font-semibold">
                {formatCurrency(order.amountCents, order.currency)}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">支付方式ID</label>
              <div className="mt-1 font-mono text-sm">{order.paymentIntentId}</div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">创建时间</label>
                <div className="mt-1 text-sm">
                  {new Date(order.createdAt).toLocaleString('zh-CN')}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">更新时间</label>
                <div className="mt-1 text-sm">
                  {new Date(order.updatedAt).toLocaleString('zh-CN')}
                </div>
              </div>
            </div>

            {order.paidAt && (
              <div>
                <label className="text-sm font-medium text-gray-500">支付时间</label>
                <div className="mt-1 text-sm">
                  {new Date(order.paidAt).toLocaleString('zh-CN')}
                </div>
              </div>
            )}

            {order.deliveredAt && (
              <div>
                <label className="text-sm font-medium text-gray-500">发货时间</label>
                <div className="mt-1 text-sm">
                  {new Date(order.deliveredAt).toLocaleString('zh-CN')}
                </div>
              </div>
            )}

            {order.refundedAt && (
              <div>
                <label className="text-sm font-medium text-gray-500">退款时间</label>
                <div className="mt-1 text-sm">
                  {new Date(order.refundedAt).toLocaleString('zh-CN')}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Information */}
        <Card>
          <CardHeader>
            <CardTitle>用户信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">用户邮箱</label>
              <div className="mt-1">{order.userEmail}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">用户ID</label>
              <div className="mt-1 font-mono text-sm">{order.userId}</div>
            </div>
          </CardContent>
        </Card>

        {/* Product Information */}
        <Card>
          <CardHeader>
            <CardTitle>商品信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">商品名称</label>
              <div className="mt-1 font-medium">{order.productName}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">平台</label>
              <div className="mt-1">{order.productPlatform}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">商品ID</label>
              <div className="mt-1 font-mono text-sm">{order.productId}</div>
            </div>
          </CardContent>
        </Card>

        {/* Activation Code */}
        {order.activationCode && (
          <Card>
            <CardHeader>
              <CardTitle>激活码信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">激活码</label>
                <div className="mt-1 font-mono text-sm bg-gray-100 p-2 rounded">
                  {order.activationCode.code}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">状态</label>
                <div className="mt-1">
                  <Badge variant="outline">{order.activationCode.status}</Badge>
                </div>
              </div>
              {order.activationCode.deliveredAt && (
                <div>
                  <label className="text-sm font-medium text-gray-500">发放时间</label>
                  <div className="mt-1 text-sm">
                    {new Date(order.activationCode.deliveredAt).toLocaleString('zh-CN')}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Metadata */}
      {order.metadata && Object.keys(order.metadata).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>元数据</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(order.metadata, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
