import { redirect } from "next/navigation";
import { getCurrentAdminUser } from "@/lib/services/admin";
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminHeader } from "@/components/admin/admin-header";

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Verify admin access
  const adminUser = await getCurrentAdminUser();

  if (!adminUser) {
    redirect('/auth/login?redirect=/admin');
  }

  return (
    <div className="min-h-screen bg-gray-50 light" data-theme="light">
      <AdminHeader user={adminUser} />
      <div className="flex">
        <AdminSidebar user={adminUser} />
        <main className="flex-1 p-6 bg-gray-50">
          <div className="light" data-theme="light">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
