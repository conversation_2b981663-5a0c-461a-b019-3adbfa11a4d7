{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate", "drizzle:push": "drizzle-kit push", "seed": "tsx scripts/seed.ts", "check-env": "tsx scripts/check-env.ts", "encode-password": "tsx scripts/encode-password.ts", "test-db": "tsx scripts/test-db.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tanstack/react-query": "^5.84.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.5", "drizzle-orm": "^0.44.4", "lucide-react": "^0.511.0", "next": "latest", "next-themes": "^0.4.6", "postgres": "^3.4.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "resend": "^4.7.0", "sonner": "^2.0.7", "stripe": "^18.4.0", "tailwind-merge": "^3.3.0", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "typescript": "^5"}}