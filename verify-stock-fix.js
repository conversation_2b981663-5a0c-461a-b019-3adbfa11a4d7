// Quick verification script for stock counting fix
const { db } = require('./lib/db');
const { products, activationCodes } = require('./lib/db/schema');
const { eq, sql } = require('drizzle-orm');

async function verifyStockFix() {
  console.log('🔍 Verifying stock counting fix...\n');

  try {
    // 1. Check all products and their active status
    console.log('1. All products and their status:');
    const allProducts = await db
      .select({
        id: products.id,
        name: products.name,
        isActive: products.isActive,
        stockCount: sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .groupBy(products.id, products.name, products.isActive);

    allProducts.forEach(product => {
      const status = product.isActive ? '✅ Active' : '❌ Inactive';
      console.log(`  - ${product.name}: ${product.stockCount} stock (${status})`);
    });

    // 2. Count active products with low stock (should match admin dashboard)
    console.log('\n2. Active products with stock <= 10:');
    const lowStockActive = allProducts.filter(p => p.isActive && p.stockCount <= 10);
    console.log(`Found ${lowStockActive.length} active products with low stock:`);
    lowStockActive.forEach(product => {
      console.log(`  - ${product.name}: ${product.stockCount} stock`);
    });

    // 3. Count all products with low stock (including inactive)
    console.log('\n3. All products (including inactive) with stock <= 10:');
    const lowStockAll = allProducts.filter(p => p.stockCount <= 10);
    console.log(`Found ${lowStockAll.length} total products with low stock:`);
    lowStockAll.forEach(product => {
      const status = product.isActive ? 'Active' : 'Inactive';
      console.log(`  - ${product.name}: ${product.stockCount} stock (${status})`);
    });

    // 4. Summary
    console.log('\n📊 Summary:');
    console.log(`Total products: ${allProducts.length}`);
    console.log(`Active products: ${allProducts.filter(p => p.isActive).length}`);
    console.log(`Inactive products: ${allProducts.filter(p => !p.isActive).length}`);
    console.log(`Active products with stock <= 10: ${lowStockActive.length}`);
    console.log(`All products with stock <= 10: ${lowStockAll.length}`);

    console.log('\n✅ The admin dashboard should now show:', lowStockActive.length, 'for "库存不足"');

  } catch (error) {
    console.error('❌ Error verifying stock fix:', error);
  }
}

// Run verification
if (require.main === module) {
  verifyStockFix().then(() => {
    console.log('\n🎉 Verification completed!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Verification failed:', error);
    process.exit(1);
  });
}

module.exports = { verifyStockFix };
