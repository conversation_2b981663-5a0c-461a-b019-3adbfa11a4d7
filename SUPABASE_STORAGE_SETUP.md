# Supabase 存储配置指南

## 🚨 问题描述
上传文件时出现错误：`StorageApiError: new row violates row-level security policy`

这是因为Supabase的行级安全策略(RLS)阻止了文件上传操作。

## 🔧 解决方案

### 步骤1：登录Supabase Dashboard
1. 访问 [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. 登录您的账户
3. 选择您的项目

### 步骤2：创建存储桶
1. 在左侧菜单中点击 **Storage**
2. 点击 **Create a new bucket**
3. 输入桶名称：`games.source`
4. 设置为 **Public bucket**（公开访问）
5. 点击 **Create bucket**

### 步骤3：配置存储策略
1. 在Storage页面，点击刚创建的 `games.source` 桶
2. 点击 **Policies** 标签
3. 点击 **New Policy**

#### 创建上传策略
1. 选择 **For full customization** 
2. 输入策略名称：`Allow authenticated users to upload`
3. 选择操作：**INSERT**
4. 输入策略定义：
```sql
-- 允许认证用户上传文件
(auth.role() = 'authenticated')
```
5. 点击 **Review** 然后 **Save policy**

#### 创建读取策略
1. 点击 **New Policy**
2. 输入策略名称：`Allow public read access`
3. 选择操作：**SELECT**
4. 输入策略定义：
```sql
-- 允许公开读取文件
true
```
5. 点击 **Review** 然后 **Save policy**

#### 创建更新策略（可选）
1. 点击 **New Policy**
2. 输入策略名称：`Allow authenticated users to update`
3. 选择操作：**UPDATE**
4. 输入策略定义：
```sql
-- 允许认证用户更新文件
(auth.role() = 'authenticated')
```
5. 点击 **Review** 然后 **Save policy**

#### 创建删除策略（可选）
1. 点击 **New Policy**
2. 输入策略名称：`Allow authenticated users to delete`
3. 选择操作：**DELETE**
4. 输入策略定义：
```sql
-- 允许认证用户删除文件
(auth.role() = 'authenticated')
```
5. 点击 **Review** 然后 **Save policy**

### 步骤4：验证配置
1. 回到您的应用
2. 尝试上传一张图片
3. 确认上传成功

## 🔐 安全策略说明

### 推荐的生产环境策略

如果您想要更严格的安全控制，可以使用以下策略：

#### 仅管理员上传策略
```sql
-- 仅允许管理员用户上传
EXISTS (
  SELECT 1 FROM users 
  WHERE users.id = auth.uid() 
  AND users.role IN ('admin', 'super_admin')
)
```

#### 文件大小限制策略
```sql
-- 限制文件大小（例如10MB）
(auth.role() = 'authenticated') 
AND (storage.foldername(name))[1] = 'products'
AND octet_length(decode(encode(storage.extension(name), 'escape'), 'hex')) <= 10485760
```

#### 文件类型限制策略
```sql
-- 仅允许图片和视频文件
(auth.role() = 'authenticated') 
AND storage.extension(name) IN ('jpg', 'jpeg', 'png', 'webp', 'mp4', 'webm', 'mov')
```

## 🛠️ 故障排除

### 问题1：桶不存在
**错误**: `Bucket not found`
**解决**: 确保已创建名为 `games.source` 的存储桶

### 问题2：权限不足
**错误**: `new row violates row-level security policy`
**解决**: 检查并添加上述存储策略

### 问题3：文件无法访问
**错误**: 文件上传成功但无法访问
**解决**: 确保添加了公开读取策略

### 问题4：认证问题
**错误**: `User not authenticated`
**解决**: 确保用户已登录且JWT token有效

## 📝 SQL脚本（快速配置）

如果您熟悉SQL，可以直接在Supabase SQL编辑器中运行以下脚本：

```sql
-- 创建存储桶（如果不存在）
INSERT INTO storage.buckets (id, name, public)
VALUES ('games.source', 'games.source', true)
ON CONFLICT (id) DO NOTHING;

-- 创建上传策略
CREATE POLICY "Allow authenticated users to upload" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'games.source' 
  AND auth.role() = 'authenticated'
);

-- 创建读取策略
CREATE POLICY "Allow public read access" ON storage.objects
FOR SELECT USING (bucket_id = 'games.source');

-- 创建更新策略
CREATE POLICY "Allow authenticated users to update" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'games.source' 
  AND auth.role() = 'authenticated'
);

-- 创建删除策略
CREATE POLICY "Allow authenticated users to delete" ON storage.objects
FOR DELETE USING (
  bucket_id = 'games.source' 
  AND auth.role() = 'authenticated'
);
```

## ✅ 验证清单

配置完成后，请验证以下功能：

- [ ] 存储桶 `games.source` 已创建
- [ ] 上传策略已配置
- [ ] 读取策略已配置
- [ ] 用户已登录
- [ ] 文件上传成功
- [ ] 文件可以正常访问
- [ ] 文件删除功能正常

## 🔗 相关链接

- [Supabase Storage 文档](https://supabase.com/docs/guides/storage)
- [Row Level Security 文档](https://supabase.com/docs/guides/auth/row-level-security)
- [Storage Policies 文档](https://supabase.com/docs/guides/storage/security/access-control)

配置完成后，您的文件上传功能应该可以正常工作了！
