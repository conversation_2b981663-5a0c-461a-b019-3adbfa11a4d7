// Test script to verify stock consistency fix
// This script can be run to check if the stock counting logic is now consistent

const { db } = require('./lib/db');
const { products, activationCodes } = require('./lib/db/schema');
const { eq, sql } = require('drizzle-orm');

async function testStockConsistency() {
  console.log('Testing stock consistency...\n');

  try {
    // Test 1: Get active products with stock <= 10 (should match admin stats)
    console.log('1. Active products with stock <= 10 (Admin Stats Logic):');
    const lowStockResult = await db
      .select({
        productId: products.id,
        productName: products.name,
        isActive: products.isActive,
        unusedCount: sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .where(eq(products.isActive, true))
      .groupBy(products.id, products.name, products.isActive)
      .having(sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0) <= 10`);

    console.log(`Found ${lowStockResult.length} active products with stock <= 10:`);
    lowStockResult.forEach(product => {
      console.log(`  - ${product.productName}: ${product.unusedCount} stock (Active: ${product.isActive})`);
    });

    // Test 2: Get active products with stock = 0 specifically
    console.log('\n2. Active products with stock = 0:');
    const zeroStockResult = await db
      .select({
        productId: products.id,
        productName: products.name,
        isActive: products.isActive,
        unusedCount: sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .where(eq(products.isActive, true))
      .groupBy(products.id, products.name, products.isActive)
      .having(sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0) = 0`);

    console.log(`Found ${zeroStockResult.length} active products with stock = 0:`);
    zeroStockResult.forEach(product => {
      console.log(`  - ${product.productName}: ${product.unusedCount} stock (Active: ${product.isActive})`);
    });

    // Test 3: Get all products (including inactive) for comparison
    console.log('\n3. All products (including inactive):');
    const allProductsResult = await db
      .select({
        productId: products.id,
        productName: products.name,
        isActive: products.isActive,
        unusedCount: sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .groupBy(products.id, products.name, products.isActive);

    console.log(`Found ${allProductsResult.length} total products:`);
    allProductsResult.forEach(product => {
      console.log(`  - ${product.productName}: ${product.unusedCount} stock (Active: ${product.isActive})`);
    });

    // Test 4: Verify the counts match expectations
    console.log('\n4. Consistency Check:');
    console.log(`Active products with stock <= 10: ${lowStockResult.length}`);
    console.log(`Active products with stock = 0: ${zeroStockResult.length}`);
    console.log(`Total products: ${allProductsResult.length}`);

    if (zeroStockResult.length <= lowStockResult.length) {
      console.log('✅ Consistency check passed: Zero stock products are included in low stock count');
    } else {
      console.log('❌ Consistency check failed: Something is wrong with the logic');
    }

  } catch (error) {
    console.error('Error testing stock consistency:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testStockConsistency().then(() => {
    console.log('\nTest completed.');
    process.exit(0);
  }).catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testStockConsistency };
