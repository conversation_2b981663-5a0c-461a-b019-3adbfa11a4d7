import { NextLogo } from "./next-logo";
import { SupabaseLogo } from "./supabase-logo";

export function <PERSON>() {
  return (
    <div className="flex flex-col gap-16 items-center text-center">
      {/* Gaming Hero Section */}
      <div className="space-y-8">
        <div className="flex justify-center items-center space-x-4 gaming-float">
          <span className="text-6xl gaming-pulse">🎮</span>
          <span className="text-6xl gaming-pulse" style={{ animationDelay: '0.5s' }}>🎯</span>
          <span className="text-6xl gaming-pulse" style={{ animationDelay: '1s' }}>🏆</span>
        </div>

        <h1 className="text-5xl lg:text-6xl font-bold gaming-text-gradient !leading-tight">
          GameVault
        </h1>

        <p className="text-xl lg:text-2xl text-muted-foreground max-w-2xl mx-auto">
          Your ultimate destination for{" "}
          <span className="font-bold text-indigo-600 dark:text-indigo-400">digital game codes</span>
          {" "}and{" "}
          <span className="font-bold text-purple-600 dark:text-purple-400">instant delivery</span>
        </p>

        <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/40 dark:to-indigo-900/40 px-4 py-2 rounded-full">
            <span>⚡</span>
            <span>Instant Delivery</span>
          </div>
          <div className="flex items-center space-x-2 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/40 dark:to-emerald-900/40 px-4 py-2 rounded-full">
            <span>🛡️</span>
            <span>100% Authentic</span>
          </div>
          <div className="flex items-center space-x-2 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/40 dark:to-pink-900/40 px-4 py-2 rounded-full">
            <span>🎯</span>
            <span>Best Prices</span>
          </div>
        </div>
      </div>

      {/* Tech Stack Credits */}
      <div className="flex flex-col gap-4 items-center opacity-60">
        <div className="flex gap-8 justify-center items-center">
          <a
            href="https://supabase.com/?utm_source=create-next-app&utm_medium=template&utm_term=nextjs"
            target="_blank"
            rel="noreferrer"
            className="hover:opacity-80 transition-opacity"
          >
            <SupabaseLogo />
          </a>
          <span className="border-l rotate-45 h-6" />
          <a href="https://nextjs.org/" target="_blank" rel="noreferrer" className="hover:opacity-80 transition-opacity">
            <NextLogo />
          </a>
        </div>
        <p className="text-xs text-muted-foreground">
          Powered by Supabase & Next.js
        </p>
      </div>

      <div className="w-full p-[1px] bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent my-8" />
    </div>
  );
}
