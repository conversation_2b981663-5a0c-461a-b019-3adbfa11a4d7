'use client';

import { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorBoundary({ error, reset }: ErrorBoundaryProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  const isDatabaseError = error.message.includes('password authentication failed') ||
                         error.message.includes('connection') ||
                         error.message.includes('database');

  const isEnvironmentError = error.message.includes('Missing env var') ||
                            error.message.includes('environment variable');

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
            <AlertTriangle className="w-6 h-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Something went wrong</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isDatabaseError && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="font-semibold text-yellow-800 mb-2">Database Connection Issue</h3>
              <p className="text-sm text-yellow-700 mb-3">
                Unable to connect to the database. This usually means:
              </p>
              <ul className="text-sm text-yellow-700 space-y-1 list-disc list-inside">
                <li>Environment variables are not set correctly</li>
                <li>Database credentials are invalid</li>
                <li>Database server is not accessible</li>
              </ul>
              <div className="mt-3 p-3 bg-yellow-100 rounded text-xs font-mono">
                Run: <code>pnpm check-env</code> to verify your setup
              </div>
            </div>
          )}

          {isEnvironmentError && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-2">Configuration Issue</h3>
              <p className="text-sm text-blue-700 mb-3">
                Required environment variables are missing.
              </p>
              <div className="mt-3 p-3 bg-blue-100 rounded text-xs">
                <p className="font-semibold mb-1">Quick fix:</p>
                <ol className="list-decimal list-inside space-y-1">
                  <li>Copy <code>.env.example</code> to <code>.env.local</code></li>
                  <li>Fill in your API keys and database URL</li>
                  <li>Restart the development server</li>
                </ol>
              </div>
            </div>
          )}

          {!isDatabaseError && !isEnvironmentError && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">Unexpected Error</h3>
              <p className="text-sm text-gray-700 mb-3">
                An unexpected error occurred. Please try refreshing the page.
              </p>
              <details className="text-xs">
                <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                  Technical details
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs">
                  {error.message}
                </pre>
              </details>
            </div>
          )}

          <div className="flex flex-col space-y-2">
            <Button onClick={reset} className="w-full">
              <RefreshCw className="w-4 h-4 mr-2" />
              Try again
            </Button>
            <Button variant="outline" onClick={() => window.location.href = '/'} className="w-full">
              Go to homepage
            </Button>
          </div>

          <div className="text-center text-xs text-gray-500">
            <p>If this problem persists, please contact support.</p>
            {error.digest && (
              <p className="mt-1">Error ID: {error.digest}</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
