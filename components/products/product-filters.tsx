'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface ProductFiltersProps {
  platforms: string[];
  regions: string[];
  selectedPlatform?: string;
  selectedRegion?: string;
}

const platformNames: Record<string, string> = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: 'Gift Card',
};

export function ProductFilters({
  platforms,
  regions,
  selectedPlatform,
  selectedRegion,
}: ProductFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const updateFilter = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }
    
    // Reset to first page when filters change
    params.delete('page');
    
    router.push(`/products?${params.toString()}`);
  };

  const clearAllFilters = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete('platform');
    params.delete('region');
    params.delete('page');
    
    router.push(`/products?${params.toString()}`);
  };

  const hasActiveFilters = selectedPlatform || selectedRegion;

  return (
    <div className="space-y-6">
      {/* Active Filters */}
      {hasActiveFilters && (
        <Card className="gaming-card border-0">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm gaming-text-gradient">🎯 Active Filters</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs h-auto p-1 hover:bg-red-50 hover:text-red-600 transition-colors"
              >
                Clear all
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {selectedPlatform && (
                <Badge
                  className="cursor-pointer bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-red-500 hover:to-red-600 text-white transition-all duration-200 border-0"
                  onClick={() => updateFilter('platform', null)}
                >
                  {platformNames[selectedPlatform] || selectedPlatform.toUpperCase()}
                  <span className="ml-1">×</span>
                </Badge>
              )}
              {selectedRegion && (
                <Badge
                  className="cursor-pointer bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-red-500 hover:to-red-600 text-white transition-all duration-200 border-0"
                  onClick={() => updateFilter('region', null)}
                >
                  {selectedRegion}
                  <span className="ml-1">×</span>
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Platform Filter */}
      <Card className="gaming-card border-0">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm gaming-text-gradient">🎮 Platform</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            {platforms.map((platform) => (
              <Button
                key={platform}
                variant={selectedPlatform === platform ? 'default' : 'ghost'}
                size="sm"
                className={`w-full justify-start text-sm h-auto py-3 transition-all duration-200 ${
                  selectedPlatform === platform
                    ? 'gaming-btn-primary'
                    : 'hover:bg-indigo-50 hover:text-indigo-600 border border-transparent hover:border-indigo-200'
                }`}
                onClick={() =>
                  updateFilter('platform', selectedPlatform === platform ? null : platform)
                }
              >
                <span className="mr-2 text-lg">
                  {platform === 'switch' && '🎮'}
                  {platform === 'ps' && '🎮'}
                  {platform === 'xbox' && '🎮'}
                  {platform === 'steam' && '💻'}
                  {platform === 'gift_card' && '🎁'}
                </span>
                {platformNames[platform] || platform.toUpperCase()}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Region Filter */}
      {regions.length > 0 && (
        <Card className="gaming-card border-0">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm gaming-text-gradient">🌍 Region</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2">
              {regions.map((region) => (
                <Button
                  key={region}
                  variant={selectedRegion === region ? 'default' : 'ghost'}
                  size="sm"
                  className={`w-full justify-start text-sm h-auto py-3 transition-all duration-200 ${
                    selectedRegion === region
                      ? 'gaming-btn-secondary'
                      : 'hover:bg-cyan-50 hover:text-cyan-600 border border-transparent hover:border-cyan-200'
                  }`}
                  onClick={() =>
                    updateFilter('region', selectedRegion === region ? null : region)
                  }
                >
                  {region}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
