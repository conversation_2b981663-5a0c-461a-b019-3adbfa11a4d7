'use client';

import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { formatAmount } from '@/lib/utils/format';
import type { ProductWithStock } from '@/lib/services/products';

interface ProductCardProps {
  product: ProductWithStock;
}

const platformIcons: Record<string, string> = {
  switch: '🎮',
  ps: '🎮',
  xbox: '🎮',
  steam: '💻',
  gift_card: '🎁',
};

const platformNames: Record<string, string> = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: 'Gift Card',
};

const platformColors: Record<string, string> = {
  switch: 'from-red-500 to-blue-500',
  ps: 'from-blue-500 to-indigo-600',
  xbox: 'from-green-500 to-emerald-600',
  steam: 'from-slate-600 to-slate-800',
  gift_card: 'from-purple-500 to-pink-500',
};

export function ProductCard({ product }: ProductCardProps) {
  const isInStock = product.stockCount > 0;
  const isLowStock = product.stockCount > 0 && product.stockCount <= 5;

  return (
    <Card className="gaming-card gaming-card-hover h-full flex flex-col border-0 group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-2xl filter drop-shadow-lg gaming-float group-hover:gaming-pulse">
              {platformIcons[product.platform] || '🎮'}
            </span>
            <Badge className={`gaming-badge-platform bg-gradient-to-r ${platformColors[product.platform] || 'from-indigo-500 to-purple-600'} text-white border-0`}>
              {platformNames[product.platform] || product.platform.toUpperCase()}
            </Badge>
          </div>
          <Badge className={`gaming-badge-stock ${isInStock ? (isLowStock ? 'low-stock' : 'in-stock') : 'out-of-stock'} border-0`}>
            {isInStock ? `${product.stockCount} in stock` : 'Out of stock'}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="flex-1">
        <div className="space-y-3">
          <div>
            <h3 className="font-semibold text-lg leading-tight line-clamp-2">
              {product.name}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              Region: {product.region}
            </p>
          </div>

          {product.description && (
            <p className="text-sm text-muted-foreground line-clamp-3">
              {product.description}
            </p>
          )}

          {isLowStock && (
            <Badge className="gaming-badge-stock low-stock border-0">
              ⚠️ Low stock
            </Badge>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-3">
        <div className="w-full space-y-3">
          <div className="flex items-center justify-between">
            <div className="text-right">
              <div className="text-2xl font-bold">
                {formatAmount(product.amountCents, product.currency)}
              </div>
              <div className="text-xs text-muted-foreground">
                Instant delivery
              </div>
            </div>
          </div>

          <div className="flex space-x-2">
            <Button asChild variant="outline" size="sm" className="flex-1 border-indigo-200 text-indigo-600 hover:bg-indigo-50 hover:border-indigo-300 transition-all duration-200">
              <Link href={`/products/${product.slug || product.id}`}>
                View Details
              </Link>
            </Button>
            <Button
              asChild
              size="sm"
              className={`flex-1 gaming-btn-primary ${!isInStock ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={!isInStock}
            >
              <Link href={`/products/${product.slug || product.id}/purchase`}>
                {isInStock ? 'Buy Now' : 'Out of Stock'}
              </Link>
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
