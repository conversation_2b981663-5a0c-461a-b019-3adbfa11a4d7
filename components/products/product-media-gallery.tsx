'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProductMediaGalleryProps {
  mainImageUrl?: string;
  videoUrl?: string;
  imageUrls?: string[];
  productName: string;
  className?: string;
}

export function ProductMediaGallery({
  mainImageUrl,
  videoUrl,
  imageUrls = [],
  productName,
  className,
}: ProductMediaGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  // Combine all media items
  const mediaItems = [];
  
  // Add video first if available
  if (videoUrl) {
    mediaItems.push({ type: 'video', url: videoUrl });
  }
  
  // Add main image
  if (mainImageUrl) {
    mediaItems.push({ type: 'image', url: mainImageUrl });
  }
  
  // Add additional images
  imageUrls.forEach(url => {
    mediaItems.push({ type: 'image', url });
  });

  // If no media, show placeholder
  if (mediaItems.length === 0) {
    return (
      <div className={cn('bg-gray-100 rounded-lg flex items-center justify-center h-96', className)}>
        <div className="text-center text-gray-500">
          <div className="text-4xl mb-2">🎮</div>
          <p>No media available</p>
        </div>
      </div>
    );
  }

  const currentMedia = mediaItems[currentIndex];

  const goToPrevious = () => {
    setCurrentIndex(prev => prev === 0 ? mediaItems.length - 1 : prev - 1);
    setIsVideoPlaying(false);
  };

  const goToNext = () => {
    setCurrentIndex(prev => prev === mediaItems.length - 1 ? 0 : prev + 1);
    setIsVideoPlaying(false);
  };

  const goToIndex = (index: number) => {
    setCurrentIndex(index);
    setIsVideoPlaying(false);
  };

  const toggleVideoPlay = () => {
    setIsVideoPlaying(!isVideoPlaying);
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main Media Display */}
      <div className="relative bg-gray-100 rounded-lg overflow-hidden aspect-video">
        {currentMedia.type === 'video' ? (
          <div className="relative w-full h-full">
            <video
              src={currentMedia.url}
              className="w-full h-full object-cover"
              controls={isVideoPlaying}
              autoPlay={isVideoPlaying}
              onPlay={() => setIsVideoPlaying(true)}
              onPause={() => setIsVideoPlaying(false)}
            />
            {!isVideoPlaying && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                <Button
                  size="lg"
                  className="rounded-full w-16 h-16 bg-white bg-opacity-90 hover:bg-opacity-100 text-black"
                  onClick={toggleVideoPlay}
                >
                  <Play className="h-6 w-6 ml-1" />
                </Button>
              </div>
            )}
          </div>
        ) : (
          <img
            src={currentMedia.url}
            alt={`${productName} - Image ${currentIndex + 1}`}
            className="w-full h-full object-cover"
          />
        )}

        {/* Navigation Arrows */}
        {mediaItems.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="sm"
              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full w-10 h-10 p-0"
              onClick={goToPrevious}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full w-10 h-10 p-0"
              onClick={goToNext}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </>
        )}

        {/* Media Counter */}
        {mediaItems.length > 1 && (
          <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
            {currentIndex + 1} / {mediaItems.length}
          </div>
        )}
      </div>

      {/* Thumbnail Navigation */}
      {mediaItems.length > 1 && (
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {mediaItems.map((media, index) => (
            <button
              key={index}
              className={cn(
                'flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all',
                currentIndex === index
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              )}
              onClick={() => goToIndex(index)}
            >
              {media.type === 'video' ? (
                <div className="relative w-full h-full bg-gray-200 flex items-center justify-center">
                  <Play className="h-6 w-6 text-gray-600" />
                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                    VIDEO
                  </div>
                </div>
              ) : (
                <img
                  src={media.url}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              )}
            </button>
          ))}
        </div>
      )}

      {/* Media Type Indicators */}
      <div className="flex items-center space-x-4 text-sm text-gray-600">
        {videoUrl && (
          <div className="flex items-center space-x-1">
            <Play className="h-4 w-4" />
            <span>Video available</span>
          </div>
        )}
        <div className="flex items-center space-x-1">
          <span>{mediaItems.filter(m => m.type === 'image').length} images</span>
        </div>
      </div>
    </div>
  );
}
