"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "./ui/button";
import { createClient } from "@/lib/supabase/client";
import { LogoutButton } from "./logout-button";
import { useEffect, useState } from "react";
import type { User } from "@supabase/supabase-js";

export function AuthButton() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const initAuth = async () => {
      try {
        // Check if environment variables are available
        if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
          setError("Missing Supabase configuration");
          setLoading(false);
          return;
        }

        const supabase = createClient();

        // Get initial session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          setError(sessionError.message);
        } else {
          setUser(session?.user ?? null);
        }

        setLoading(false);

        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, session) => {
            setUser(session?.user ?? null);
            setLoading(false);
          }
        );

        return () => {
          subscription.unsubscribe();
        };
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error");
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const handleSignIn = () => {
    router.push("/auth/login");
  };

  const handleSignUp = () => {
    router.push("/auth/sign-up");
  };

  // Show error state
  if (error) {
    return (
      <div className="flex gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={handleSignIn}
        >
          Sign in
        </Button>
        <Button
          size="sm"
          variant="default"
          onClick={handleSignUp}
        >
          Sign up
        </Button>
      </div>
    );
  }

  // Show loading state
  if (loading) {
    return (
      <div className="flex gap-2">
        <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  // Show authenticated state
  if (user) {
    return (
      <div className="flex items-center gap-4">
        Hey, {user.email}!
        <LogoutButton />
      </div>
    );
  }

  // Show unauthenticated state - using Link components for guaranteed navigation
  return (
    <div className="flex gap-2">
      <Link
        href="/auth/login"
        className="inline-flex items-center justify-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
        style={{
          pointerEvents: 'auto',
          minWidth: '60px',
          height: '32px',
          textDecoration: 'none'
        }}
      >
        Sign in
      </Link>
      <Link
        href="/auth/sign-up"
        className="inline-flex items-center justify-center px-3 py-1.5 text-xs font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
        style={{
          pointerEvents: 'auto',
          minWidth: '60px',
          height: '32px',
          textDecoration: 'none'
        }}
      >
        Sign up
      </Link>
    </div>
  );
}
