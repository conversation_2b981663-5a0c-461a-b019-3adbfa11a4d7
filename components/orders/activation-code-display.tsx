'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>, EyeOff, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ActivationCodeDisplayProps {
  code: string;
}

export function ActivationCodeDisplay({ code }: ActivationCodeDisplayProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const { toast } = useToast();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setIsCopied(true);
      toast({
        title: 'Code copied!',
        description: 'Activation code has been copied to your clipboard.',
      });
      
      // Reset copy state after 2 seconds
      setTimeout(() => setIsCopied(false), 2000);
    } catch {
      toast({
        title: 'Failed to copy',
        description: 'Please manually select and copy the code.',
        variant: 'destructive',
      });
    }
  };

  const maskedCode = code.replace(/./g, '•');
  const displayCode = isVisible ? code : maskedCode;

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-lg">Activation Code</h3>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsVisible(!isVisible)}
            >
              {isVisible ? (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Hide
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Show
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopy}
              disabled={isCopied}
            >
              {isCopied ? (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </>
              )}
            </Button>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded border font-mono text-lg text-center tracking-wider">
          {displayCode}
        </div>
        
        {!isVisible && (
          <p className="text-sm text-muted-foreground mt-2 text-center">
            Click &quot;Show&quot; to reveal your activation code
          </p>
        )}
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <div className="flex items-start space-x-2">
          <span className="text-yellow-600 mt-0.5">⚠️</span>
          <div className="text-sm">
            <p className="font-semibold text-yellow-800 mb-1">Important:</p>
            <ul className="text-yellow-700 space-y-1">
              <li>• Keep this code safe and don&apos;t share it with anyone</li>
              <li>• This code can only be used once</li>
              <li>• Save this code before closing this page</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
