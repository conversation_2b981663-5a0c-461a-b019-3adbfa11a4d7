'use client';

import { Button } from '@/components/ui/button';
import { Copy } from 'lucide-react';
import { toast } from 'sonner';

interface AdminActivationCodeCopyButtonProps {
  code: string;
}

export function AdminActivationCodeCopyButton({ code }: AdminActivationCodeCopyButtonProps) {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success('激活码已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败');
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleCopy}
      title="复制完整激活码"
    >
      <Copy className="h-3 w-3" />
    </Button>
  );
}
