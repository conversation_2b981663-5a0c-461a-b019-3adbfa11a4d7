import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import { getRecentOrders } from '@/lib/services/admin-dashboard';
import { formatCurrency } from '@/lib/utils';

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-blue-100 text-blue-800',
  delivered: 'bg-green-100 text-green-800',
  refunded: 'bg-red-100 text-red-800',
  under_review: 'bg-orange-100 text-orange-800',
};

const statusLabels = {
  pending: '待支付',
  paid: '已支付',
  delivered: '已发货',
  refunded: '已退款',
  under_review: '审核中',
};

export async function AdminRecentOrders() {
  const orders = await getRecentOrders(5);

  if (orders.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        暂无订单数据
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {orders.map((order) => (
        <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-sm">{order.productName}</span>
              <Badge 
                className={statusColors[order.status as keyof typeof statusColors]}
                variant="secondary"
              >
                {statusLabels[order.status as keyof typeof statusLabels]}
              </Badge>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {order.userEmail} • {formatCurrency(order.amountCents, 'CNY')}
            </div>
            <div className="text-xs text-gray-400">
              {new Date(order.createdAt).toLocaleString('zh-CN')}
            </div>
          </div>
          <Button asChild variant="ghost" size="sm">
            <Link href={`/admin/orders/${order.id}`}>
              <ExternalLink className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      ))}
      
      <div className="pt-2 border-t">
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/admin/orders">
            查看所有订单
          </Link>
        </Button>
      </div>
    </div>
  );
}
