'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { FileUpload } from '@/components/ui/file-upload';
import { MultiFileUpload } from '@/components/ui/multi-file-upload';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { toast } from 'sonner';
import { Loader2, Upload, X, Image, Video, FileText } from 'lucide-react';
import { useProductOptions } from '@/lib/hooks/use-product-options';
import type { UploadResult } from '@/lib/supabase/storage';

interface ProductFormData {
  name: string;
  description: string;
  detailContent: string;
  platform: string;
  category: string;
  price: string;
  currency: string;
  isActive: boolean;
  // Media fields
  mainImageUrl: string;
  videoUrl: string;
  imageUrls: string[];
  // Legacy field for backward compatibility
  imageUrl: string;
  systemRequirements: string;
  features: string[];
  tags: string[];
}

export function AdminAddProductForm() {
  const router = useRouter();
  const { options, loading: optionsLoading, error: optionsError } = useProductOptions();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    detailContent: '',
    platform: '',
    category: '',
    price: '',
    currency: 'CNY',
    isActive: true,
    mainImageUrl: '',
    videoUrl: '',
    imageUrls: [],
    imageUrl: '',
    systemRequirements: '',
    features: [],
    tags: [],
  });

  const [newFeature, setNewFeature] = useState('');
  const [newTag, setNewTag] = useState('');

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Media upload handlers
  const handleMainImageUpload = (result: UploadResult) => {
    console.log('Main image upload result:', result);
    if (result.success && result.url) {
      handleInputChange('mainImageUrl', result.url);
      toast.success('主图上传成功！');
    } else {
      console.error('Main image upload failed:', result.error);
      toast.error(result.error || '主图上传失败');
    }
  };

  const handleVideoUpload = (result: UploadResult) => {
    console.log('Video upload result:', result);
    if (result.success && result.url) {
      handleInputChange('videoUrl', result.url);
      toast.success('视频上传成功！');
    } else {
      console.error('Video upload failed:', result.error);
      toast.error(result.error || '视频上传失败');
    }
  };

  const handleImagesUpload = (results: UploadResult[]) => {
    const successfulUploads = results.filter(r => r.success && r.url);
    const errors = results.filter(r => !r.success);

    if (successfulUploads.length > 0) {
      const newUrls = successfulUploads.map(r => r.url!);
      handleInputChange('imageUrls', [...formData.imageUrls, ...newUrls]);
      toast.success(`成功上传 ${successfulUploads.length} 张图片！`);
    }

    if (errors.length > 0) {
      toast.error(`${errors.length} 张图片上传失败`);
    }
  };

  const removeMainImage = () => {
    handleInputChange('mainImageUrl', '');
  };

  const removeVideo = () => {
    handleInputChange('videoUrl', '');
  };

  const removeImage = (index: number) => {
    const newImageUrls = formData.imageUrls.filter((_, i) => i !== index);
    handleInputChange('imageUrls', newImageUrls);
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(f => f !== feature)
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.name.trim()) {
      toast.error('请输入商品名称');
      return;
    }
    
    if (!formData.platform) {
      toast.error('请选择平台');
      return;
    }
    
    if (!formData.category) {
      toast.error('请选择分类');
      return;
    }
    
    if (!formData.price || parseFloat(formData.price) <= 0) {
      toast.error('请输入有效的价格');
      return;
    }

    setIsLoading(true);

    try {
      // Prepare product data
      const productData = {
        name: formData.name,
        description: formData.description,
        detailContent: formData.detailContent,
        platform: formData.platform,
        category: formData.category,
        region: 'Global', // Default region for now
        currency: formData.currency,
        amountCents: Math.round(parseFloat(formData.price) * 100),
        isActive: formData.isActive,
        mainImageUrl: formData.mainImageUrl,
        videoUrl: formData.videoUrl,
        imageUrls: formData.imageUrls,
        systemRequirements: formData.systemRequirements,
        features: formData.features,
        tags: formData.tags,
      };

      // Call API to create product
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || '创建商品失败');
      }

      toast.success('商品创建成功！');
      router.push('/admin/products');
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error(error instanceof Error ? error.message : '创建商品失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (optionsLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载表单选项...</span>
      </div>
    );
  }

  if (optionsError) {
    return (
      <div className="text-center py-8 text-red-600">
        <div className="text-lg font-medium">加载失败</div>
        <div className="text-sm mt-2">{optionsError}</div>
      </div>
    );
  }

  if (!options) {
    return null;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">商品名称 *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="输入商品名称"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">商品描述</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="输入商品描述"
            rows={4}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="platform">平台 *</Label>
            <Select value={formData.platform} onValueChange={(value) => handleInputChange('platform', value)}>
              <SelectTrigger>
                <SelectValue placeholder="选择平台" />
              </SelectTrigger>
              <SelectContent>
                {options.platforms.map((platform) => (
                  <SelectItem key={platform.value} value={platform.value}>
                    {platform.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">分类 *</Label>
            <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                {options.categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="price">价格 *</Label>
            <Input
              id="price"
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value)}
              placeholder="0.00"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="currency">货币</Label>
            <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {options.currencies.map((currency) => (
                  <SelectItem key={currency.value} value={currency.value}>
                    {currency.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Media Upload Section */}
        <div className="space-y-6">
          <Separator />
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <Image className="h-5 w-5 mr-2" />
              商品媒体
            </h3>

            {/* Main Image Upload */}
            <div className="space-y-2 mb-6">
              <Label>主图 *</Label>
              <p className="text-sm text-gray-500">商品的主要展示图片，将在商品列表和详情页顶部显示</p>
              <FileUpload
                onUpload={handleMainImageUpload}
                onRemove={removeMainImage}
                currentFile={formData.mainImageUrl}
                accept="image/*"
                folder="products/main"
                placeholder="上传商品主图"
              />
            </div>

            {/* Video Upload */}
            <div className="space-y-2 mb-6">
              <Label>商品视频</Label>
              <p className="text-sm text-gray-500">可选：上传商品展示视频，将优先于图片在详情页展示</p>
              <FileUpload
                onUpload={handleVideoUpload}
                onRemove={removeVideo}
                currentFile={formData.videoUrl}
                accept="video/*"
                folder="products/videos"
                placeholder="上传商品视频（可选）"
              />
            </div>

            {/* Additional Images Upload */}
            <div className="space-y-2">
              <Label>商品图片库</Label>
              <p className="text-sm text-gray-500">上传更多商品图片，用户可以在详情页浏览</p>
              <MultiFileUpload
                onUpload={handleImagesUpload}
                onRemove={removeImage}
                currentFiles={formData.imageUrls}
                maxFiles={8}
                folder="products/gallery"
                placeholder="上传更多商品图片"
              />
            </div>
          </div>
        </div>

        {/* Rich Text Content Section */}
        <div className="space-y-6">
          <Separator />
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              商品详情
            </h3>

            <div className="space-y-2">
              <Label>详细描述</Label>
              <RichTextEditor
                value={formData.detailContent}
                onChange={(value) => handleInputChange('detailContent', value)}
                placeholder=""
                minHeight={300}
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="systemRequirements">系统要求</Label>
          <Textarea
            id="systemRequirements"
            value={formData.systemRequirements}
            onChange={(e) => handleInputChange('systemRequirements', e.target.value)}
            placeholder="输入系统要求"
            rows={3}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isActive"
            checked={formData.isActive}
            onCheckedChange={(checked) => handleInputChange('isActive', checked)}
          />
          <Label htmlFor="isActive">立即上架</Label>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={isLoading}
        >
          取消
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          创建商品
        </Button>
      </div>
    </form>
  );
}
