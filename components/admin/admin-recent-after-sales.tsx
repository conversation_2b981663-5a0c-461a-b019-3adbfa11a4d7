import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import { getRecentAfterSales } from '@/lib/services/admin-dashboard';

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  resolved: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
};

const statusLabels = {
  pending: '待处理',
  resolved: '已解决',
  rejected: '已拒绝',
};

const issueTypeLabels = {
  invalid_code: '无效激活码',
  used_code: '激活码已使用',
  wrong_region: '区域错误',
  other: '其他问题',
};

export async function AdminRecentAfterSales() {
  const afterSales = await getRecentAfterSales(5);

  if (afterSales.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        暂无售后工单
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {afterSales.map((item) => (
        <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-sm">
                {issueTypeLabels[item.issueType as keyof typeof issueTypeLabels] || item.issueType}
              </span>
              <Badge 
                className={statusColors[item.status as keyof typeof statusColors]}
                variant="secondary"
              >
                {statusLabels[item.status as keyof typeof statusLabels]}
              </Badge>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              订单: {item.orderId.slice(0, 8)}...
            </div>
            <div className="text-xs text-gray-400">
              {new Date(item.createdAt).toLocaleString('zh-CN')}
            </div>
          </div>
          <Button asChild variant="ghost" size="sm">
            <Link href={`/admin/after-sales/${item.id}`}>
              <ExternalLink className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      ))}
      
      <div className="pt-2 border-t">
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/admin/after-sales">
            查看所有工单
          </Link>
        </Button>
      </div>
    </div>
  );
}
