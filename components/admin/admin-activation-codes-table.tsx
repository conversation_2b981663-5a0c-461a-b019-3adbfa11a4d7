import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { getAdminActivationCodes } from '@/lib/services/admin-activation-codes';
import { AdminActivationCodesPagination } from './admin-activation-codes-pagination';
import { AdminActivationCodeActions } from './admin-activation-code-actions';
import { AdminActivationCodeCopyButton } from './admin-activation-code-copy-button';

interface AdminActivationCodesTableProps {
  searchParams: {
    page?: string;
    product?: string;
    status?: string;
    search?: string;
  };
}

const statusColors = {
  unused: 'bg-green-100 text-green-800',
  locked: 'bg-yellow-100 text-yellow-800',
  used: 'bg-gray-100 text-gray-800',
  invalid: 'bg-red-100 text-red-800',
};

const statusLabels = {
  unused: '未使用',
  locked: '已锁定',
  used: '已使用',
  invalid: '无效',
};

const platformLabels = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: '礼品卡',
};

export async function AdminActivationCodesTable({ searchParams }: AdminActivationCodesTableProps) {
  const filters = {
    page: parseInt(searchParams.page || '1'),
    product: searchParams.product,
    status: searchParams.status,
    search: searchParams.search,
  };

  const { codes, pagination } = await getAdminActivationCodes(filters);

  if (codes.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="text-lg font-medium">暂无激活码数据</div>
        <div className="text-sm mt-2">尝试调整筛选条件或添加新的激活码</div>
      </div>
    );
  }

  const maskCode = (code: string) => {
    if (code.length <= 8) return code;
    return code.slice(0, 4) + '****' + code.slice(-4);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          共 {pagination.total} 条记录，第 {pagination.page} / {pagination.totalPages} 页
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>激活码</TableHead>
              <TableHead>商品</TableHead>
              <TableHead>平台</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>锁定订单</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {codes.map((code) => (
              <TableRow key={code.id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-sm">
                      {maskCode(code.code)}
                    </span>
                    <AdminActivationCodeCopyButton code={code.code} />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm font-medium">{code.productName}</div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {platformLabels[code.productPlatform as keyof typeof platformLabels]}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge 
                    className={statusColors[code.status as keyof typeof statusColors]}
                    variant="secondary"
                  >
                    {statusLabels[code.status as keyof typeof statusLabels]}
                  </Badge>
                </TableCell>
                <TableCell>
                  {code.lockedByOrderId ? (
                    <span className="font-mono text-xs">
                      {code.lockedByOrderId.slice(0, 8)}...
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </TableCell>
                <TableCell className="text-sm text-gray-600">
                  {new Date(code.createdAt).toLocaleString('zh-CN')}
                </TableCell>
                <TableCell>
                  <AdminActivationCodeActions code={code} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <AdminActivationCodesPagination pagination={pagination} />
    </div>
  );
}
