import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Plus } from 'lucide-react';
import { getStockAlerts } from '@/lib/services/admin-dashboard';

const platformLabels = {
  switch: 'Nintendo Switch',
  ps: 'PlayStation',
  xbox: 'Xbox',
  steam: 'Steam',
  gift_card: '礼品卡',
};

export async function AdminStockAlerts() {
  const alerts = await getStockAlerts();

  if (alerts.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="flex flex-col items-center space-y-2">
          <div className="text-green-600">
            ✓ 所有商品库存充足
          </div>
          <div className="text-sm">
            没有需要补充库存的商品
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {alerts.map((alert) => (
        <div key={alert.productId} className="flex items-center justify-between p-4 border border-orange-200 rounded-lg bg-orange-50">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <div>
              <div className="font-medium text-sm">{alert.productName}</div>
              <div className="text-xs text-gray-600">
                {platformLabels[alert.platform as keyof typeof platformLabels]} • 
                剩余库存: {alert.currentStock} / 阈值: {alert.threshold}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-orange-700 border-orange-300">
              库存不足
            </Badge>
            <Button asChild variant="outline" size="sm">
              <Link href={`/admin/activation-codes?product=${alert.productId}`}>
                <Plus className="h-4 w-4 mr-1" />
                补充库存
              </Link>
            </Button>
          </div>
        </div>
      ))}
      
      <div className="pt-2 border-t">
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/admin/activation-codes">
            管理所有激活码
          </Link>
        </Button>
      </div>
    </div>
  );
}
