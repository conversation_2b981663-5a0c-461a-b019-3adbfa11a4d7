'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { MoreHorizontal, XCircle, RotateCcw, Eye } from 'lucide-react';
import { toast } from 'sonner';
import type { AdminActivationCode } from '@/lib/services/admin-activation-codes';

interface AdminActivationCodeActionsProps {
  code: AdminActivationCode;
}

export function AdminActivationCodeActions({ code }: AdminActivationCodeActionsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    type: string;
    title: string;
    description: string;
  } | null>(null);

  const handleAction = async (action: string) => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/admin/activation-codes/${code.id}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });

      if (!response.ok) {
        throw new Error('操作失败');
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('操作成功');
        router.refresh();
      } else {
        throw new Error(result.error || '操作失败');
      }
    } catch (error) {
      console.error('Action error:', error);
      toast.error(error instanceof Error ? error.message : '操作失败');
    } finally {
      setIsLoading(false);
      setShowConfirmDialog(false);
      setPendingAction(null);
    }
  };

  const confirmAction = (type: string, title: string, description: string) => {
    setPendingAction({ type, title, description });
    setShowConfirmDialog(true);
  };

  const canMarkInvalid = ['unused', 'locked'].includes(code.status);
  const canReactivate = code.status === 'invalid';

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" disabled={isLoading}>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>激活码操作</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem
            onClick={() => navigator.clipboard.writeText(code.code)}
          >
            <Eye className="mr-2 h-4 w-4" />
            查看完整激活码
          </DropdownMenuItem>
          
          {canMarkInvalid && (
            <DropdownMenuItem
              onClick={() => confirmAction(
                'mark_invalid',
                '标记为无效',
                '确定要将此激活码标记为无效吗？此操作不可撤销。'
              )}
              className="text-red-600"
            >
              <XCircle className="mr-2 h-4 w-4" />
              标记为无效
            </DropdownMenuItem>
          )}
          
          {canReactivate && (
            <DropdownMenuItem
              onClick={() => confirmAction(
                'reactivate',
                '重新激活',
                '确定要重新激活此激活码吗？'
              )}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              重新激活
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{pendingAction?.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {pendingAction?.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => pendingAction && handleAction(pendingAction.type)}
              disabled={isLoading}
            >
              {isLoading ? '处理中...' : '确认'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
