import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Key, 
  Package, 
  CheckCircle, 
  AlertTriangle,
} from 'lucide-react';
import { getActivationCodesStats } from '@/lib/services/admin-activation-codes';

export async function AdminActivationCodesStats() {
  const stats = await getActivationCodesStats();

  const statCards = [
    {
      title: '总激活码数',
      value: stats.totalCodes.toString(),
      icon: Key,
      color: 'text-blue-600',
    },
    {
      title: '可用激活码',
      value: stats.unusedCodes.toString(),
      icon: Package,
      color: 'text-green-600',
    },
    {
      title: '已使用激活码',
      value: stats.usedCodes.toString(),
      icon: CheckCircle,
      color: 'text-gray-600',
    },
    {
      title: '库存不足商品',
      value: stats.lowStockProducts.toString(),
      icon: AlertTriangle,
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
