'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, X } from 'lucide-react';

export function AdminActivationCodesFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    product: searchParams.get('product') || '',
    status: searchParams.get('status') || '',
  });

  const [products, setProducts] = useState<Array<{ id: string; name: string }>>([]);

  // Load products for filter dropdown
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const response = await fetch('/api/admin/products');
        if (response.ok) {
          const data = await response.json();
          // Extract products from the new API response format
          const products = data.data?.products || [];
          // Map to the format expected by the filter dropdown
          const productOptions = products.map((product: any) => ({
            id: product.id,
            name: product.name,
          }));
          setProducts(productOptions);
        }
      } catch (error) {
        console.error('Failed to load products:', error);
      }
    };

    loadProducts();
  }, []);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value === 'all' ? '' : value }));
  };

  const applyFilters = () => {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });

    router.push(`/admin/activation-codes?${params.toString()}`);
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      product: '',
      status: '',
    });
    router.push('/admin/activation-codes');
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="space-y-2">
        <Label htmlFor="search">搜索</Label>
        <Input
          id="search"
          placeholder="激活码、商品名称"
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="product">商品</Label>
        <Select value={filters.product || 'all'} onValueChange={(value) => handleFilterChange('product', value)}>
          <SelectTrigger>
            <SelectValue placeholder="选择商品" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部商品</SelectItem>
            {products.map((product) => (
              <SelectItem key={product.id} value={product.id}>
                {product.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">状态</Label>
        <Select value={filters.status || 'all'} onValueChange={(value) => handleFilterChange('status', value)}>
          <SelectTrigger>
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="unused">未使用</SelectItem>
            <SelectItem value="locked">已锁定</SelectItem>
            <SelectItem value="used">已使用</SelectItem>
            <SelectItem value="invalid">无效</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-end space-x-2">
        <Button onClick={applyFilters} className="flex-1">
          <Search className="h-4 w-4 mr-2" />
          筛选
        </Button>
        <Button variant="outline" onClick={clearFilters}>
          <X className="h-4 w-4 mr-2" />
          重置
        </Button>
      </div>
    </div>
  );
}
