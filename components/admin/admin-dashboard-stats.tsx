import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  DollarSign, 
  Users, 
  AlertTriangle,
  TrendingUp,
  TrendingDown,
} from 'lucide-react';
import { getDashboardStats } from '@/lib/services/admin-dashboard';

export async function AdminDashboardStats() {
  const stats = await getDashboardStats();

  const statCards = [
    {
      title: '今日订单',
      value: stats.todayOrders.toString(),
      change: stats.todayOrdersChange,
      icon: ShoppingCart,
      color: 'text-blue-600',
    },
    {
      title: '今日收入',
      value: `¥${(stats.todayRevenue / 100).toFixed(2)}`,
      change: stats.todayRevenueChange,
      icon: DollarSign,
      color: 'text-green-600',
    },
    {
      title: '活跃用户',
      value: stats.activeUsers.toString(),
      change: stats.activeUsersChange,
      icon: Users,
      color: 'text-purple-600',
    },
    {
      title: '待处理工单',
      value: stats.pendingAfterSales.toString(),
      change: stats.pendingAfterSalesChange,
      icon: AlertTriangle,
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <div className="flex items-center space-x-1 text-xs text-gray-600">
              {stat.change > 0 ? (
                <>
                  <TrendingUp className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">+{stat.change}%</span>
                </>
              ) : stat.change < 0 ? (
                <>
                  <TrendingDown className="h-3 w-3 text-red-600" />
                  <span className="text-red-600">{stat.change}%</span>
                </>
              ) : (
                <span>无变化</span>
              )}
              <span>vs 昨日</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
