'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  ShoppingCart,
  Key,
  HeadphonesIcon,
  Users,
  FileText,
  BarChart3,
  Settings,
  Package,
} from 'lucide-react';
import type { AdminUser } from '@/lib/services/admin';

interface AdminSidebarProps {
  user: AdminUser;
}

const navigationItems = [
  {
    name: '仪表板',
    href: '/admin',
    icon: LayoutDashboard,
    exact: true,
  },
  {
    name: '订单管理',
    href: '/admin/orders',
    icon: ShoppingCart,
  },
  {
    name: '商品管理',
    href: '/admin/products',
    icon: Package,
  },
  {
    name: '激活码管理',
    href: '/admin/activation-codes',
    icon: Key,
  },
  {
    name: '售后工单',
    href: '/admin/after-sales',
    icon: HeadphonesIcon,
  },
  {
    name: '用户管理',
    href: '/admin/users',
    icon: Users,
    requireSuperAdmin: true,
  },
  {
    name: '审计日志',
    href: '/admin/audit-logs',
    icon: FileText,
  },
  {
    name: '监控报告',
    href: '/admin/reports',
    icon: BarChart3,
  },
  {
    name: '系统设置',
    href: '/admin/settings',
    icon: Settings,
    requireSuperAdmin: true,
  },
];

export function AdminSidebar({ user }: AdminSidebarProps) {
  const pathname = usePathname();

  const filteredItems = navigationItems.filter(item => 
    !item.requireSuperAdmin || user.isSuperAdmin
  );

  return (
    <aside className="w-64 bg-white border-r border-gray-200 min-h-screen">
      <nav className="p-4 space-y-2">
        {filteredItems.map((item) => {
          const isActive = item.exact 
            ? pathname === item.href
            : pathname.startsWith(item.href);

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',
                isActive
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon className="h-5 w-5" />
              <span>{item.name}</span>
            </Link>
          );
        })}
      </nav>
    </aside>
  );
}
