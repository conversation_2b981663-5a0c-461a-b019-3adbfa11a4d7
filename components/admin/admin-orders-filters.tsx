'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, X } from 'lucide-react';

export function AdminOrdersFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    status: searchParams.get('status') || '',
    risk: searchParams.get('risk') || '',
    date_from: searchParams.get('date_from') || '',
    date_to: searchParams.get('date_to') || '',
  });

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value === 'all' ? '' : value }));
  };

  const applyFilters = () => {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });

    router.push(`/admin/orders?${params.toString()}`);
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      risk: '',
      date_from: '',
      date_to: '',
    });
    router.push('/admin/orders');
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <div className="space-y-2">
        <Label htmlFor="search">搜索</Label>
        <Input
          id="search"
          placeholder="订单ID、用户邮箱、商品名称"
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">订单状态</Label>
        <Select value={filters.status || 'all'} onValueChange={(value) => handleFilterChange('status', value)}>
          <SelectTrigger>
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="pending">待支付</SelectItem>
            <SelectItem value="paid">已支付</SelectItem>
            <SelectItem value="delivered">已发货</SelectItem>
            <SelectItem value="refunded">已退款</SelectItem>
            <SelectItem value="under_review">审核中</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="risk">风险等级</Label>
        <Select value={filters.risk || 'all'} onValueChange={(value) => handleFilterChange('risk', value)}>
          <SelectTrigger>
            <SelectValue placeholder="选择风险等级" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部等级</SelectItem>
            <SelectItem value="low">低风险</SelectItem>
            <SelectItem value="medium">中风险</SelectItem>
            <SelectItem value="high">高风险</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="date_from">开始日期</Label>
        <Input
          id="date_from"
          type="date"
          value={filters.date_from}
          onChange={(e) => handleFilterChange('date_from', e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="date_to">结束日期</Label>
        <Input
          id="date_to"
          type="date"
          value={filters.date_to}
          onChange={(e) => handleFilterChange('date_to', e.target.value)}
        />
      </div>

      <div className="flex items-end space-x-2 md:col-span-2 lg:col-span-5">
        <Button onClick={applyFilters} className="flex-1">
          <Search className="h-4 w-4 mr-2" />
          应用筛选
        </Button>
        <Button variant="outline" onClick={clearFilters}>
          <X className="h-4 w-4 mr-2" />
          清除
        </Button>
      </div>
    </div>
  );
}
