'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, X, Loader2 } from 'lucide-react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useProductOptions } from '@/lib/hooks/use-product-options';

// Simple debounce function
function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  }) as T;
}

export function AdminProductsFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { options, loading, error } = useProductOptions();

  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    status: searchParams.get('status') || '',
    platform: searchParams.get('platform') || '',
    category: searchParams.get('category') || '',
  });

  // Auto-apply filters function
  const autoApplyFilters = useCallback((newFilters: typeof filters) => {
    const params = new URLSearchParams();

    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });

    // Reset to first page when applying filters
    params.delete('page');

    router.push(`/admin/products?${params.toString()}`);
  }, [router]);

  // Debounced version for search input
  const debouncedAutoApply = useCallback(
    debounce((newFilters: typeof filters) => {
      autoApplyFilters(newFilters);
    }, 500),
    [autoApplyFilters]
  );

  const handleFilterChange = (key: string, value: string) => {
    const newValue = value === 'all' ? '' : value;
    const newFilters = { ...filters, [key]: newValue };
    setFilters(newFilters);

    // Auto-apply filters immediately for dropdowns, with debounce for search
    if (key === 'search') {
      debouncedAutoApply(newFilters);
    } else {
      autoApplyFilters(newFilters);
    }
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: '',
      status: '',
      platform: '',
      category: '',
    };
    setFilters(clearedFilters);
    router.push('/admin/products');
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  // Update local state when URL changes (e.g., browser back/forward)
  useEffect(() => {
    setFilters({
      search: searchParams.get('search') || '',
      status: searchParams.get('status') || '',
      platform: searchParams.get('platform') || '',
      category: searchParams.get('category') || '',
    });
  }, [searchParams]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载筛选选项...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-600">
        <div className="text-lg font-medium">加载失败</div>
        <div className="text-sm mt-2">{error}</div>
      </div>
    );
  }

  if (!options) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">搜索</label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="搜索商品名称..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Status */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">状态</label>
          <Select value={filters.status || 'all'} onValueChange={(value) => handleFilterChange('status', value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              {options.statuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Platform */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">平台</label>
          <Select value={filters.platform || 'all'} onValueChange={(value) => handleFilterChange('platform', value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择平台" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部平台</SelectItem>
              {options.platforms.map((platform) => (
                <SelectItem key={platform.value} value={platform.value}>
                  {platform.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">分类</label>
          <Select value={filters.category || 'all'} onValueChange={(value) => handleFilterChange('category', value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部分类</SelectItem>
              {options.categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Action Buttons */}
      {hasActiveFilters && (
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={clearFilters}>
            <X className="h-4 w-4 mr-2" />
            清除筛选
          </Button>
          <span className="text-sm text-gray-500">
            筛选条件会自动应用
          </span>
        </div>
      )}
    </div>
  );
}
