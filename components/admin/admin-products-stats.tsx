import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import {
  Package,
  ShoppingCart,
  AlertTriangle,
  TrendingUp,
} from 'lucide-react';
import { db } from '@/lib/db';
import { products, activationCodes, orders } from '@/lib/db/schema';
import { eq, sql, and } from 'drizzle-orm';

// Get real products statistics from database
async function getProductsStats() {
  try {
    // Get total products count
    const totalProductsResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(products);

    // Get active products count (temporarily disabled due to schema mismatch)
    // const activeProductsResult = await db
    //   .select({ count: sql<number>`count(*)` })
    //   .from(products)
    //   .where(eq(products.status, 'active'));

    // Get low stock products (less than or equal to 10 unused activation codes, only active products)
    const lowStockResult = await db
      .select({
        productId: products.id,
        unusedCount: sql<number>`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0)`,
      })
      .from(products)
      .leftJoin(activationCodes, eq(products.id, activationCodes.productId))
      .where(eq(products.isActive, true))
      .groupBy(products.id)
      .having(sql`COALESCE(COUNT(${activationCodes.id}) FILTER (WHERE ${activationCodes.status} = 'unused'), 0) <= 10`);

    // Get total sales from delivered orders
    const totalSalesResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(orders)
      .where(eq(orders.status, 'delivered'));

    const totalProducts = totalProductsResult[0]?.count || 0;
    const activeProducts = totalProducts; // Fallback since status field is not available
    const lowStockProducts = lowStockResult.length;
    const totalSales = totalSalesResult[0]?.count || 0;

    return {
      totalProducts,
      activeProducts,
      lowStockProducts,
      totalSales,
    };
  } catch (error) {
    console.error('Error getting products stats:', error);
    // Return fallback data
    return {
      totalProducts: 0,
      activeProducts: 0,
      lowStockProducts: 0,
      totalSales: 0,
    };
  }
}

export async function AdminProductsStats() {
  const stats = await getProductsStats();

  const statsCards = [
    {
      title: '总商品数',
      value: stats.totalProducts,
      icon: Package,
      description: '平台上的所有商品',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '在售商品',
      value: stats.activeProducts,
      icon: ShoppingCart,
      description: '当前可购买的商品',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '库存不足',
      value: stats.lowStockProducts,
      icon: AlertTriangle,
      description: '需要补充库存的商品',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: '总销量',
      value: stats.totalSales,
      icon: TrendingUp,
      description: '所有商品的总销量',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsCards.map((stat) => {
        const Icon = stat.icon;
        
        return (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {stat.value.toLocaleString()}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
