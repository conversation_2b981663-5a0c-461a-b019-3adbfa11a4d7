import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  HeadphonesIcon,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-react';

// Mock data for demonstration - replace with actual data fetching
async function getAfterSalesStats() {
  return {
    totalTickets: 156,
    pendingTickets: 23,
    resolvedTickets: 118,
    rejectedTickets: 15,
    avgResponseTime: 4,
  };
}

export async function AdminAfterSalesStats() {
  const stats = await getAfterSalesStats();

  const statCards = [
    {
      title: '总工单数',
      value: stats.totalTickets.toString(),
      icon: HeadphonesIcon,
      color: 'text-blue-600',
    },
    {
      title: '待处理工单',
      value: stats.pendingTickets.toString(),
      icon: Clock,
      color: 'text-orange-600',
    },
    {
      title: '已解决工单',
      value: stats.resolvedTickets.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
    },
    {
      title: '已拒绝工单',
      value: stats.rejectedTickets.toString(),
      icon: XCircle,
      color: 'text-red-600',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            {stat.title === '待处理工单' && stats.avgResponseTime && (
              <div className="text-xs text-gray-600 mt-1">
                平均响应时间: {stats.avgResponseTime}小时
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
