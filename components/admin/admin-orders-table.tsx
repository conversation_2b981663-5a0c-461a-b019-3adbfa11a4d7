import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ExternalLink, Eye } from 'lucide-react';
import { getAdminOrders } from '@/lib/services/admin-orders';
import { formatCurrency } from '@/lib/utils';
import { AdminOrdersPagination } from './admin-orders-pagination';

interface AdminOrdersTableProps {
  searchParams: {
    page?: string;
    status?: string;
    risk?: string;
    search?: string;
    date_from?: string;
    date_to?: string;
  };
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-blue-100 text-blue-800',
  delivered: 'bg-green-100 text-green-800',
  refunded: 'bg-red-100 text-red-800',
  under_review: 'bg-orange-100 text-orange-800',
};

const statusLabels = {
  pending: '待支付',
  paid: '已支付',
  delivered: '已发货',
  refunded: '已退款',
  under_review: '审核中',
};

const riskColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-red-100 text-red-800',
};

const riskLabels = {
  low: '低风险',
  medium: '中风险',
  high: '高风险',
};

export async function AdminOrdersTable({ searchParams }: AdminOrdersTableProps) {
  const filters = {
    page: parseInt(searchParams.page || '1'),
    status: searchParams.status,
    risk: searchParams.risk,
    search: searchParams.search,
    dateFrom: searchParams.date_from,
    dateTo: searchParams.date_to,
  };

  const { orders, pagination } = await getAdminOrders(filters);

  if (orders.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="text-lg font-medium">暂无订单数据</div>
        <div className="text-sm mt-2">尝试调整筛选条件或检查数据库连接</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          共 {pagination.total} 条记录，第 {pagination.page} / {pagination.totalPages} 页
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>订单ID</TableHead>
              <TableHead>用户</TableHead>
              <TableHead>商品</TableHead>
              <TableHead>金额</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>风险等级</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id}>
                <TableCell className="font-mono text-xs">
                  {order.id.slice(0, 8)}...
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div className="font-medium">{order.userEmail}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div className="font-medium">{order.productName}</div>
                    <div className="text-gray-500">{order.productPlatform}</div>
                  </div>
                </TableCell>
                <TableCell>
                  {formatCurrency(order.amountCents, order.currency)}
                </TableCell>
                <TableCell>
                  <Badge 
                    className={statusColors[order.status as keyof typeof statusColors]}
                    variant="secondary"
                  >
                    {statusLabels[order.status as keyof typeof statusLabels]}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge 
                    className={riskColors[order.riskLevel as keyof typeof riskColors]}
                    variant="secondary"
                  >
                    {riskLabels[order.riskLevel as keyof typeof riskLabels]}
                  </Badge>
                </TableCell>
                <TableCell className="text-sm text-gray-600">
                  {new Date(order.createdAt).toLocaleString('zh-CN')}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button asChild variant="ghost" size="sm">
                      <Link href={`/admin/orders/${order.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button asChild variant="ghost" size="sm">
                      <Link href={`/orders/${order.id}`} target="_blank">
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <AdminOrdersPagination pagination={pagination} />
    </div>
  );
}
