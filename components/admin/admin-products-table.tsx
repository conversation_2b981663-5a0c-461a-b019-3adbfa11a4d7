import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Eye, Edit, Trash2, Package } from 'lucide-react';
import Link from 'next/link';
import { statusConfig, platformLabels, categoryLabels } from '@/lib/utils/product-labels';
import { getAdminProducts, AdminProductFilters } from '@/lib/services/products';

interface AdminProductsTableProps {
  searchParams: Promise<{
    page?: string;
    status?: string;
    platform?: string;
    search?: string;
    category?: string;
  }>;
}

export async function AdminProductsTable({ searchParams }: AdminProductsTableProps) {
  // Await searchParams and convert to filters
  const params = await searchParams;
  const filters: AdminProductFilters = {
    search: params.search || undefined,
    status: params.status || undefined,
    platform: params.platform || undefined,
    category: params.category || undefined,
    page: params.page ? parseInt(params.page) : 1,
    limit: 20,
  };

  // Fetch products from database
  const { products: productList, total } = await getAdminProducts(filters);

  if (productList.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <div className="text-lg font-medium">暂无商品</div>
        <div className="text-sm mt-2">当前没有符合条件的商品</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          共 {total} 个商品，当前显示第 {(filters.page! - 1) * filters.limit! + 1}-{Math.min(filters.page! * filters.limit!, total)} 条
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50/50 border-b border-gray-200">
                <TableHead className="w-[280px] min-w-[280px] font-medium text-gray-700">商品</TableHead>
                <TableHead className="w-[90px] min-w-[90px] text-center font-medium text-gray-700">平台</TableHead>
                <TableHead className="w-[70px] min-w-[70px] text-center font-medium text-gray-700">分类</TableHead>
                <TableHead className="w-[90px] min-w-[90px] text-right font-medium text-gray-700">价格</TableHead>
                <TableHead className="w-[70px] min-w-[70px] text-center font-medium text-gray-700">状态</TableHead>
                <TableHead className="w-[70px] min-w-[70px] text-center font-medium text-gray-700">库存</TableHead>
                <TableHead className="w-[70px] min-w-[70px] text-center font-medium text-gray-700">销量</TableHead>
                <TableHead className="w-[130px] min-w-[130px] font-medium text-gray-700">更新时间</TableHead>
                <TableHead className="w-[110px] min-w-[110px] text-center font-medium text-gray-700">操作</TableHead>
              </TableRow>
            </TableHeader>
          <TableBody>
            {productList.map((product) => {
              // Map product status to display info
              const getStatusInfo = (isActive: boolean) => {
                if (isActive) {
                  return { label: '在售', color: 'text-green-600 bg-green-50' };
                } else {
                  return { label: '下架', color: 'text-red-600 bg-red-50' };
                }
              };

              const statusInfo = getStatusInfo(product.isActive);
              const isLowStock = product.stockCount <= 10;

              return (
                <TableRow key={product.id} className="hover:bg-gray-50/50">
                  <TableCell className="py-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Package className="h-5 w-5 text-gray-400" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium text-gray-900 truncate">
                          {product.name}
                        </div>
                        <div className="text-xs text-gray-500 font-mono truncate">
                          {product.id}
                        </div>
                      </div>
                    </div>
                  </TableCell>

                  <TableCell className="text-center py-4">
                    <Badge variant="secondary" className="text-xs">
                      {platformLabels[product.platform as keyof typeof platformLabels]}
                    </Badge>
                  </TableCell>

                  <TableCell className="text-center py-4">
                    <div className="text-sm text-gray-600">
                      {product.platform === 'gift_card' ? '礼品卡' :
                       product.platform === 'steam' ? '游戏' :
                       product.platform === 'ps' ? '游戏' :
                       product.platform === 'xbox' ? '游戏' :
                       product.platform === 'switch' ? '游戏' : '其他'}
                    </div>
                  </TableCell>

                  <TableCell className="text-right py-4">
                    <div className="font-medium text-gray-900">
                      ¥{(product.amountCents / 100).toFixed(2)}
                    </div>
                  </TableCell>

                  <TableCell className="text-center py-4">
                    <Badge className={`${statusInfo.color} border-0`} variant="secondary">
                      {statusInfo.label}
                    </Badge>
                  </TableCell>

                  <TableCell className="text-center py-4">
                    <div className={`font-medium text-sm ${isLowStock ? 'text-red-600' : 'text-gray-900'}`}>
                      {product.stockCount}
                    </div>
                    {isLowStock && (
                      <div className="text-xs text-red-500 mt-1">库存不足</div>
                    )}
                  </TableCell>

                  <TableCell className="text-center py-4">
                    <div className="font-medium text-sm text-gray-900">
                      {product.totalSales || 0}
                    </div>
                  </TableCell>

                  <TableCell className="py-4">
                    <div className="text-sm text-gray-600">
                      {new Date(product.updatedAt).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </TableCell>
                  
                  <TableCell className="text-center py-4">
                    <div className="flex items-center justify-center space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                        asChild
                      >
                        <Link href={`/admin/products/${product.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-500 hover:text-blue-600 hover:bg-blue-50"
                        asChild
                      >
                        <Link href={`/admin/products/${product.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        </div>
      </div>

      {/* Pagination would go here */}
      <div className="flex justify-center">
        <div className="text-sm text-gray-500">
          显示第 {(filters.page! - 1) * filters.limit! + 1}-{Math.min(filters.page! * filters.limit!, total)} 条，共 {total} 条记录
        </div>
      </div>
    </div>
  );
}
