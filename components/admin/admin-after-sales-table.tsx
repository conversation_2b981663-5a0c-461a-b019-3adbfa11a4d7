import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Eye, MessageSquare, Clock, CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';

interface AdminAfterSalesTableProps {
  searchParams: {
    page?: string;
    status?: string;
    issue_type?: string;
    search?: string;
    date_from?: string;
    date_to?: string;
  };
}

// Mock data for demonstration - replace with actual data fetching
const mockTickets = [
  {
    id: 'AS001',
    user_email: '<EMAIL>',
    issue_type: 'activation_issue',
    status: 'pending',
    subject: '激活码无法使用',
    description: '购买的游戏激活码提示已被使用',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z',
    priority: 'high',
    order_id: 'ORD001',
  },
  {
    id: 'AS002',
    user_email: '<EMAIL>',
    issue_type: 'refund_request',
    status: 'in_progress',
    subject: '申请退款',
    description: '游戏不符合预期，希望退款',
    created_at: '2024-01-14T15:20:00Z',
    updated_at: '2024-01-15T09:15:00Z',
    priority: 'medium',
    order_id: 'ORD002',
  },
  {
    id: 'AS003',
    user_email: '<EMAIL>',
    issue_type: 'technical_support',
    status: 'resolved',
    subject: '下载问题',
    description: '无法下载购买的游戏',
    created_at: '2024-01-13T08:45:00Z',
    updated_at: '2024-01-14T16:30:00Z',
    priority: 'low',
    order_id: 'ORD003',
  },
];

const statusConfig = {
  pending: {
    label: '待处理',
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock,
  },
  in_progress: {
    label: '处理中',
    color: 'bg-blue-100 text-blue-800',
    icon: MessageSquare,
  },
  resolved: {
    label: '已解决',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
  closed: {
    label: '已关闭',
    color: 'bg-gray-100 text-gray-800',
    icon: XCircle,
  },
};

const issueTypeLabels = {
  activation_issue: '激活问题',
  refund_request: '退款申请',
  technical_support: '技术支持',
  account_issue: '账户问题',
  other: '其他',
};

const priorityConfig = {
  low: { label: '低', color: 'bg-gray-100 text-gray-800' },
  medium: { label: '中', color: 'bg-yellow-100 text-yellow-800' },
  high: { label: '高', color: 'bg-red-100 text-red-800' },
};

export async function AdminAfterSalesTable({ searchParams }: AdminAfterSalesTableProps) {
  // In a real implementation, you would fetch data based on searchParams
  const tickets = mockTickets;

  if (tickets.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="text-lg font-medium">暂无售后工单</div>
        <div className="text-sm mt-2">当前没有符合条件的售后工单</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          共 {tickets.length} 个工单
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>工单号</TableHead>
              <TableHead>用户邮箱</TableHead>
              <TableHead>问题类型</TableHead>
              <TableHead>主题</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>优先级</TableHead>
              <TableHead>关联订单</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tickets.map((ticket) => {
              const statusInfo = statusConfig[ticket.status as keyof typeof statusConfig];
              const StatusIcon = statusInfo.icon;
              const priorityInfo = priorityConfig[ticket.priority as keyof typeof priorityConfig];

              return (
                <TableRow key={ticket.id}>
                  <TableCell>
                    <div className="font-mono text-sm font-medium">
                      {ticket.id}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="text-sm">{ticket.user_email}</div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="text-sm">
                      {issueTypeLabels[ticket.issue_type as keyof typeof issueTypeLabels]}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="max-w-xs">
                      <div className="text-sm font-medium truncate">
                        {ticket.subject}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {ticket.description}
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Badge className={statusInfo.color} variant="secondary">
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {statusInfo.label}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <Badge className={priorityInfo.color} variant="secondary">
                      {priorityInfo.label}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    {ticket.order_id ? (
                      <Link 
                        href={`/admin/orders?search=${ticket.order_id}`}
                        className="text-blue-600 hover:text-blue-800 font-mono text-sm"
                      >
                        {ticket.order_id}
                      </Link>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </TableCell>
                  
                  <TableCell className="text-sm text-gray-600">
                    {new Date(ticket.created_at).toLocaleString('zh-CN')}
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                      >
                        <Link href={`/admin/after-sales/${ticket.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* Pagination would go here */}
      <div className="flex justify-center">
        <div className="text-sm text-gray-500">
          显示第 1-{tickets.length} 条，共 {tickets.length} 条记录
        </div>
      </div>
    </div>
  );
}
