'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  MoreHorizontal, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  AlertTriangle,
  Send,
} from 'lucide-react';
import { toast } from 'sonner';
import type { AdminOrderDetail } from '@/lib/services/admin-orders';

interface AdminOrderActionsProps {
  order: AdminOrderDetail;
}

export function AdminOrderActions({ order }: AdminOrderActionsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    type: string;
    title: string;
    description: string;
  } | null>(null);

  const handleAction = async (action: string) => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/admin/orders/${order.id}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });

      if (!response.ok) {
        throw new Error('操作失败');
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('操作成功');
        router.refresh();
      } else {
        throw new Error(result.error || '操作失败');
      }
    } catch (error) {
      console.error('Action error:', error);
      toast.error(error instanceof Error ? error.message : '操作失败');
    } finally {
      setIsLoading(false);
      setShowConfirmDialog(false);
      setPendingAction(null);
    }
  };

  const confirmAction = (type: string, title: string, description: string) => {
    setPendingAction({ type, title, description });
    setShowConfirmDialog(true);
  };

  const canMarkAsPaid = order.status === 'pending';
  const canMarkAsDelivered = order.status === 'paid';
  const canMarkAsUnderReview = ['pending', 'paid'].includes(order.status);
  const canRefund = ['paid', 'delivered'].includes(order.status);
  const canResendCode = order.status === 'delivered' && order.activationCode;

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" disabled={isLoading}>
            <MoreHorizontal className="h-4 w-4" />
            操作
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>订单操作</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {canMarkAsPaid && (
            <DropdownMenuItem
              onClick={() => confirmAction(
                'mark_paid',
                '标记为已支付',
                '确定要将此订单标记为已支付吗？'
              )}
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              标记为已支付
            </DropdownMenuItem>
          )}
          
          {canMarkAsDelivered && (
            <DropdownMenuItem
              onClick={() => confirmAction(
                'mark_delivered',
                '标记为已发货',
                '确定要将此订单标记为已发货吗？这将尝试分配激活码。'
              )}
            >
              <Send className="mr-2 h-4 w-4" />
              标记为已发货
            </DropdownMenuItem>
          )}
          
          {canMarkAsUnderReview && (
            <DropdownMenuItem
              onClick={() => confirmAction(
                'mark_under_review',
                '标记为审核中',
                '确定要将此订单标记为审核中吗？'
              )}
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              标记为审核中
            </DropdownMenuItem>
          )}
          
          {canRefund && (
            <DropdownMenuItem
              onClick={() => confirmAction(
                'refund',
                '退款订单',
                '确定要退款此订单吗？此操作不可撤销。'
              )}
              className="text-red-600"
            >
              <XCircle className="mr-2 h-4 w-4" />
              退款订单
            </DropdownMenuItem>
          )}
          
          {canResendCode && (
            <DropdownMenuItem
              onClick={() => confirmAction(
                'resend_code',
                '重新发送激活码',
                '确定要重新发送激活码邮件吗？'
              )}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              重新发送激活码
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{pendingAction?.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {pendingAction?.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => pendingAction && handleAction(pendingAction.type)}
              disabled={isLoading}
            >
              {isLoading ? '处理中...' : '确认'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
