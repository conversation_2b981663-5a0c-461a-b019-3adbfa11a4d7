'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Al<PERSON>Triangle, CheckCircle, Settings, X, ExternalLink } from 'lucide-react';

export function SetupStatus() {
  const [isVisible, setIsVisible] = useState(true);
  const [isClient, setIsClient] = useState(false);
  const [configStatus, setConfigStatus] = useState({
    database: false,
    stripe: false,
    email: false,
  });

  useEffect(() => {
    setIsClient(true);
    // Check configuration status via API
    const checkConfig = async () => {
      try {
        const response = await fetch('/api/config-status');
        if (response.ok) {
          const status = await response.json();
          setConfigStatus(status);
        }
      } catch (error) {
        console.error('Failed to check config status:', error);
        // Fallback to client-side check for public keys
        const stripeKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '';
        setConfigStatus({
          database: false, // Can't check server-side vars from client
          stripe: Boolean(stripeKey && !stripeKey.includes('your-')),
          email: false, // Can't check server-side vars from client
        });
      }
    };

    checkConfig();
  }, []);

  // Don't render on server side to avoid hydration mismatch
  if (!isClient) {
    return null;
  }

  // Don't show if everything is configured
  if (Object.values(configStatus).every(Boolean)) {
    return null;
  }

  // Don't show if user dismissed it
  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="shadow-lg"
        >
          <Settings className="h-4 w-4 mr-2" />
          Setup Status
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md">
      <Card className="gaming-card shadow-2xl border-0 gaming-glow bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/30 dark:to-amber-900/30">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <CardTitle className="text-sm text-orange-800 font-bold">⚙️ Setup Required</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0 text-orange-600 hover:text-orange-800 hover:bg-orange-100 rounded-full transition-all duration-200"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-orange-700">
            Some features are disabled because services aren&apos;t configured yet.
          </div>

          {/* Configuration Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium">Database</span>
              {configStatus.database ? (
                <Badge variant="default" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready
                </Badge>
              ) : (
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Setup Required
                </Badge>
              )}
            </div>

            <div className="flex items-center justify-between">
              <span className="text-xs font-medium">Stripe Payments</span>
              {configStatus.stripe ? (
                <Badge variant="default" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Optional
                </Badge>
              )}
            </div>

            <div className="flex items-center justify-between">
              <span className="text-xs font-medium">Email Service</span>
              {configStatus.email ? (
                <Badge variant="default" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Optional
                </Badge>
              )}
            </div>
          </div>

          {/* Current Features */}
          <div className="border-t pt-3">
            <div className="text-xs font-medium text-orange-800 mb-2">Available Features:</div>
            <div className="flex flex-wrap gap-1">
              <Badge variant="default" className="text-xs">
                Product Browsing
              </Badge>
              <Badge variant={configStatus.database ? "default" : "secondary"} className="text-xs">
                User Auth
              </Badge>
              <Badge variant={configStatus.stripe ? "default" : "secondary"} className="text-xs">
                Payments
              </Badge>
              <Badge variant={configStatus.email ? "default" : "secondary"} className="text-xs">
                Email
              </Badge>
            </div>
          </div>

          {/* Demo Mode Notice */}
          {!configStatus.stripe && (
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-3">
              <div className="text-xs font-medium text-blue-800 mb-1">🎮 Demo Mode Active</div>
              <div className="text-xs text-blue-700">
                You can browse products, but payments are disabled. Complete the setup to enable all features.
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs gaming-btn-secondary border-0"
              onClick={() => window.open('/SETUP_GUIDE.md', '_blank')}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Setup Guide
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs gaming-btn-primary border-0"
              onClick={() => {
                // Open terminal or show command
                navigator.clipboard.writeText('pnpm check-env');
                alert('Command copied: pnpm check-env\nRun this in your terminal to check configuration.');
              }}
            >
              Check Config
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
