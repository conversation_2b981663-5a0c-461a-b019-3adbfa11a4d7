'use client';

import { cn } from '@/lib/utils';

interface RichTextDisplayProps {
  content: string;
  className?: string;
}

export function RichTextDisplay({ content, className }: RichTextDisplayProps) {
  if (!content) {
    return null;
  }

  return (
    <div 
      className={cn(
        'prose prose-sm max-w-none',
        'prose-headings:text-gray-900 prose-headings:font-semibold',
        'prose-p:text-gray-700 prose-p:leading-relaxed',
        'prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline',
        'prose-strong:text-gray-900 prose-strong:font-semibold',
        'prose-ul:list-disc prose-ol:list-decimal',
        'prose-li:text-gray-700 prose-li:my-1',
        'prose-blockquote:border-l-4 prose-blockquote:border-gray-300 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-gray-600',
        'prose-pre:bg-gray-100 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto',
        'prose-code:bg-gray-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm',
        'prose-img:rounded-lg prose-img:shadow-sm',
        className
      )}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
