'use client';

import { cn } from '@/lib/utils';

interface GamingLoaderProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export function GamingLoader({ className, size = 'md', text }: GamingLoaderProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>
      <div className="relative">
        {/* Outer ring */}
        <div className={cn(
          'rounded-full border-4 border-indigo-200 dark:border-indigo-800',
          sizeClasses[size]
        )} />
        
        {/* Inner spinning ring */}
        <div className={cn(
          'absolute top-0 left-0 rounded-full border-4 border-transparent border-t-indigo-500 gaming-spin',
          sizeClasses[size]
        )} />
        
        {/* Gaming icon in center */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <span className="text-indigo-500 gaming-pulse">🎮</span>
        </div>
      </div>
      
      {text && (
        <p className="text-sm text-muted-foreground gaming-pulse">
          {text}
        </p>
      )}
    </div>
  );
}

export function GamingCardSkeleton() {
  return (
    <div className="gaming-card h-full flex flex-col border-0 animate-pulse">
      <div className="p-6 pb-3">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-indigo-200 to-purple-200 rounded"></div>
            <div className="w-20 h-4 bg-gradient-to-r from-indigo-200 to-purple-200 rounded"></div>
          </div>
          <div className="w-16 h-4 bg-gradient-to-r from-green-200 to-emerald-200 rounded"></div>
        </div>
      </div>
      
      <div className="px-6 flex-1">
        <div className="w-3/4 h-6 bg-gradient-to-r from-slate-200 to-slate-300 rounded mb-2"></div>
        <div className="w-full h-4 bg-gradient-to-r from-slate-200 to-slate-300 rounded mb-4"></div>
        <div className="w-1/2 h-4 bg-gradient-to-r from-slate-200 to-slate-300 rounded"></div>
      </div>
      
      <div className="p-6 pt-4">
        <div className="flex items-center justify-between mb-4">
          <div className="w-20 h-6 bg-gradient-to-r from-indigo-200 to-purple-200 rounded"></div>
          <div className="w-16 h-4 bg-gradient-to-r from-amber-200 to-orange-200 rounded"></div>
        </div>
        
        <div className="flex space-x-2">
          <div className="flex-1 h-8 bg-gradient-to-r from-slate-200 to-slate-300 rounded"></div>
          <div className="flex-1 h-8 bg-gradient-to-r from-indigo-200 to-purple-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}

export function GamingPageLoader({ text = 'Loading awesome games...' }: { text?: string }) {
  return (
    <div className="min-h-screen gaming-bg-pattern flex items-center justify-center">
      <div className="text-center">
        <GamingLoader size="lg" text={text} />
        <div className="mt-8 space-y-2">
          <div className="flex justify-center space-x-2">
            <div className="w-2 h-2 bg-indigo-500 rounded-full gaming-pulse"></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full gaming-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-cyan-500 rounded-full gaming-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
          <p className="text-xs text-muted-foreground">
            Preparing your gaming experience...
          </p>
        </div>
      </div>
    </div>
  );
}
