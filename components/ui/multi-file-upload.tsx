'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { X, Upload, Image, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { uploadMultipleFiles, validateMediaFile, type UploadResult } from '@/lib/supabase/storage';

interface MultiFileUploadProps {
  onUpload: (results: UploadResult[]) => void;
  onRemove?: (index: number) => void;
  accept?: string;
  maxFiles?: number;
  maxSize?: number;
  folder?: string;
  className?: string;
  disabled?: boolean;
  currentFiles?: string[];
  placeholder?: string;
}

export function MultiFileUpload({
  onUpload,
  onRemove,
  accept = 'image/*',
  maxFiles = 5,
  maxSize = 10 * 1024 * 1024, // 10MB
  folder,
  className,
  disabled = false,
  currentFiles = [],
  placeholder = 'Click to upload images or drag and drop',
}: MultiFileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFilesSelect = useCallback(async (files: File[]) => {
    // Validate files
    const validFiles: File[] = [];
    const errors: string[] = [];

    for (const file of files) {
      const validation = validateMediaFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    }

    if (errors.length > 0) {
      onUpload([{
        success: false,
        error: errors.join(', ')
      }]);
      return;
    }

    if (currentFiles.length + validFiles.length > maxFiles) {
      onUpload([{
        success: false,
        error: `Maximum ${maxFiles} files allowed`
      }]);
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => Math.min(prev + 10, 90));
    }, 200);

    try {
      const results = await uploadMultipleFiles(validFiles, 'games.source', folder);
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
        onUpload(results);
      }, 500);
    } catch (error) {
      clearInterval(progressInterval);
      setIsUploading(false);
      setUploadProgress(0);
      onUpload([{
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }]);
    }
  }, [onUpload, folder, currentFiles.length, maxFiles]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled || isUploading) return;
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFilesSelect(files);
    }
  }, [disabled, isUploading, handleFilesSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !isUploading) {
      setIsDragging(true);
    }
  }, [disabled, isUploading]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleClick = () => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFilesSelect(Array.from(files));
    }
  };

  const renderImageGrid = () => {
    if (currentFiles.length === 0) return null;

    return (
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
        {currentFiles.map((url, index) => (
          <div key={index} className="relative group">
            <img
              src={url}
              alt={`Product image ${index + 1}`}
              className="w-full h-24 object-cover rounded-lg border"
            />
            {onRemove && (
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => onRemove(index)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        ))}
      </div>
    );
  };

  const canAddMore = currentFiles.length < maxFiles;

  return (
    <div className={cn('space-y-4', className)}>
      {renderImageGrid()}
      
      {canAddMore && (
        <div
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
            isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400',
            disabled && 'opacity-50 cursor-not-allowed',
            isUploading && 'pointer-events-none'
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={handleClick}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            multiple
            onChange={handleFileChange}
            className="hidden"
            disabled={disabled || isUploading}
          />
          
          {isUploading ? (
            <div className="space-y-2">
              <Upload className="h-8 w-8 mx-auto text-blue-500 animate-pulse" />
              <p className="text-sm text-gray-600">Uploading images...</p>
              <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center justify-center">
                <Image className="h-6 w-6 text-gray-400 mr-2" />
                <Plus className="h-4 w-4 text-gray-400" />
              </div>
              <p className="text-sm text-gray-600">{placeholder}</p>
              <p className="text-xs text-gray-500">
                {currentFiles.length}/{maxFiles} images • Up to {Math.round(maxSize / 1024 / 1024)}MB each
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
