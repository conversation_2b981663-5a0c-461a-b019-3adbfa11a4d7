'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { X, Upload, Image, Video, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import { uploadFile, validateMediaFile, isVideoFile, isImageFile, type UploadResult } from '@/lib/supabase/storage';

interface FileUploadProps {
  onUpload: (result: UploadResult) => void;
  onRemove?: () => void;
  accept?: string;
  maxSize?: number;
  folder?: string;
  className?: string;
  disabled?: boolean;
  currentFile?: string;
  placeholder?: string;
  showPreview?: boolean;
}

export function FileUpload({
  onUpload,
  onRemove,
  accept = 'image/*,video/*',
  maxSize = 10 * 1024 * 1024, // 10MB
  folder,
  className,
  disabled = false,
  currentFile,
  placeholder = 'Click to upload or drag and drop',
  showPreview = true,
}: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback(async (file: File) => {
    // Validate file
    const validation = validateMediaFile(file);
    if (!validation.valid) {
      onUpload({
        success: false,
        error: validation.error
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => Math.min(prev + 10, 90));
    }, 200);

    try {
      const result = await uploadFile(file, 'games.source', folder);
      clearInterval(progressInterval);
      setUploadProgress(100);

      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
        onUpload(result);
      }, 500);
    } catch (error) {
      clearInterval(progressInterval);
      setIsUploading(false);
      setUploadProgress(0);

      console.error('File upload error:', error);

      let errorMessage = 'Upload failed';
      if (error instanceof Error) {
        errorMessage = error.message;
        if (error.message.includes('row-level security policy')) {
          errorMessage = '文件上传权限不足。请按照 SUPABASE_STORAGE_SETUP.md 配置存储权限。';
        }
      }

      onUpload({
        success: false,
        error: errorMessage
      });
    }
  }, [onUpload, folder]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled || isUploading) return;
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [disabled, isUploading, handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !isUploading) {
      setIsDragging(true);
    }
  }, [disabled, isUploading]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleClick = () => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const getFileIcon = (url: string) => {
    if (url.match(/\.(mp4|webm|mov)$/i)) {
      return <Video className="h-8 w-8 text-blue-500" />;
    } else if (url.match(/\.(jpg|jpeg|png|webp)$/i)) {
      return <Image className="h-8 w-8 text-green-500" />;
    }
    return <FileText className="h-8 w-8 text-gray-500" />;
  };

  const renderPreview = () => {
    if (!currentFile || !showPreview) return null;

    if (currentFile.match(/\.(jpg|jpeg|png|webp)$/i)) {
      return (
        <div className="relative">
          <img
            src={currentFile}
            alt="Preview"
            className="w-full h-32 object-cover rounded-lg"
          />
          {onRemove && (
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2 h-6 w-6 p-0"
              onClick={onRemove}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      );
    }

    if (currentFile.match(/\.(mp4|webm|mov)$/i)) {
      return (
        <div className="relative">
          <video
            src={currentFile}
            className="w-full h-32 object-cover rounded-lg"
            controls
          />
          {onRemove && (
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2 h-6 w-6 p-0"
              onClick={onRemove}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      );
    }

    return (
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          {getFileIcon(currentFile)}
          <span className="text-sm text-gray-600 truncate">
            {currentFile.split('/').pop()}
          </span>
        </div>
        {onRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={onRemove}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  };

  if (currentFile && showPreview) {
    return (
      <div className={cn('space-y-2', className)}>
        {renderPreview()}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
          isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400',
          disabled && 'opacity-50 cursor-not-allowed',
          isUploading && 'pointer-events-none'
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileChange}
          className="hidden"
          disabled={disabled || isUploading}
        />
        
        {isUploading ? (
          <div className="space-y-2">
            <Upload className="h-8 w-8 mx-auto text-blue-500 animate-pulse" />
            <p className="text-sm text-gray-600">Uploading...</p>
            <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
          </div>
        ) : (
          <div className="space-y-2">
            <Upload className="h-8 w-8 mx-auto text-gray-400" />
            <p className="text-sm text-gray-600">{placeholder}</p>
            <p className="text-xs text-gray-500">
              Supports images and videos up to {Math.round(maxSize / 1024 / 1024)}MB
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
