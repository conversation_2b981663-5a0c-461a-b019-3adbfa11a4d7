'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  Link, 
  Image, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  Quote,
  Code,
  Undo,
  Redo
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  minHeight?: number;
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = 'Enter product details...',
  className,
  disabled = false,
  minHeight = 200,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  const executeCommand = useCallback((command: string, value?: string) => {
    if (disabled) return;
    
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    
    // Update content
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  }, [disabled, onChange]);

  const handleInput = useCallback(() => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  }, [onChange]);

  const handlePaste = useCallback((e: React.ClipboardEvent) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    document.execCommand('insertText', false, text);
  }, []);

  const insertLink = useCallback(() => {
    const url = prompt('Enter URL:');
    if (url) {
      executeCommand('createLink', url);
    }
  }, [executeCommand]);

  const insertImage = useCallback(() => {
    const url = prompt('Enter image URL:');
    if (url) {
      executeCommand('insertImage', url);
    }
  }, [executeCommand]);

  const toolbarButtons = [
    {
      icon: Bold,
      command: 'bold',
      title: 'Bold (Ctrl+B)',
    },
    {
      icon: Italic,
      command: 'italic',
      title: 'Italic (Ctrl+I)',
    },
    {
      icon: Underline,
      command: 'underline',
      title: 'Underline (Ctrl+U)',
    },
    { separator: true },
    {
      icon: AlignLeft,
      command: 'justifyLeft',
      title: 'Align Left',
    },
    {
      icon: AlignCenter,
      command: 'justifyCenter',
      title: 'Align Center',
    },
    {
      icon: AlignRight,
      command: 'justifyRight',
      title: 'Align Right',
    },
    { separator: true },
    {
      icon: List,
      command: 'insertUnorderedList',
      title: 'Bullet List',
    },
    {
      icon: ListOrdered,
      command: 'insertOrderedList',
      title: 'Numbered List',
    },
    { separator: true },
    {
      icon: Quote,
      command: 'formatBlock',
      value: 'blockquote',
      title: 'Quote',
    },
    {
      icon: Code,
      command: 'formatBlock',
      value: 'pre',
      title: 'Code Block',
    },
    { separator: true },
    {
      icon: Link,
      action: insertLink,
      title: 'Insert Link',
    },
    {
      icon: Image,
      action: insertImage,
      title: 'Insert Image',
    },
    { separator: true },
    {
      icon: Undo,
      command: 'undo',
      title: 'Undo (Ctrl+Z)',
    },
    {
      icon: Redo,
      command: 'redo',
      title: 'Redo (Ctrl+Y)',
    },
  ];

  return (
    <div className={cn('border rounded-lg overflow-hidden', className)}>
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b bg-gray-50">
        {toolbarButtons.map((button, index) => {
          if ('separator' in button) {
            return <Separator key={index} orientation="vertical" className="h-6" />;
          }

          const Icon = button.icon;
          
          return (
            <Button
              key={index}
              type="button"
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              title={button.title}
              disabled={disabled}
              onClick={() => {
                if (button.action) {
                  button.action();
                } else {
                  executeCommand(button.command, button.value);
                }
              }}
            >
              <Icon className="h-4 w-4" />
            </Button>
          );
        })}
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        className={cn(
          'p-4 outline-none prose prose-sm max-w-none',
          'focus:ring-2 focus:ring-blue-500 focus:ring-inset',
          disabled && 'opacity-50 cursor-not-allowed bg-gray-50'
        )}
        style={{ minHeight }}
        dangerouslySetInnerHTML={{ __html: value }}
        onInput={handleInput}
        onPaste={handlePaste}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        data-placeholder={placeholder}
      />

      {/* Placeholder */}
      {!value && !isFocused && placeholder && (
        <div
          className="absolute inset-0 top-[60px] left-4 text-gray-400 pointer-events-none"
          style={{ marginTop: '1rem' }}
        >
          {placeholder}
        </div>
      )}

      <style jsx>{`
        [contenteditable]:empty:before {
          content: ${placeholder ? 'attr(data-placeholder)' : '""'};
          color: #9ca3af;
          pointer-events: none;
        }
        
        .prose blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: #6b7280;
        }
        
        .prose pre {
          background-color: #f3f4f6;
          padding: 1rem;
          border-radius: 0.375rem;
          overflow-x: auto;
          font-family: 'Courier New', monospace;
        }
        
        .prose img {
          max-width: 100%;
          height: auto;
          border-radius: 0.375rem;
        }
        
        .prose a {
          color: #3b82f6;
          text-decoration: underline;
        }
        
        .prose ul, .prose ol {
          padding-left: 1.5rem;
        }
        
        .prose li {
          margin: 0.25rem 0;
        }
      `}</style>
    </div>
  );
}
